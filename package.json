{"name": "shopee-fastify", "version": "1.0.0", "description": "Ecommerce", "main": "src/index.js", "type": "module", "scripts": {"dev": "nodemon src/index.js", "start": "node src/index.js"}, "author": "<PERSON>", "license": "MIT", "dependencies": {"@aws-sdk/client-s3": "^3.876.0", "@aws-sdk/lib-storage": "^3.876.0", "@elastic/elasticsearch": "^9.0.2", "@fastify/cors": "^11.0.1", "@fastify/formbody": "^8.0.2", "@fastify/helmet": "^13.0.1", "@fastify/multipart": "^9.0.3", "ajv-errors": "^3.0.0", "ajv-formats": "^3.0.1", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "decimal.js": "^10.6.0", "dotenv": "^16.5.0", "fastify": "^5.3.3", "fastify-multer": "^2.0.3", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "moment-timezone": "^0.6.0", "morgan": "^1.10.0", "multer": "^2.0.2", "multer-s3": "^3.0.1", "mysql2": "^3.14.1", "nodemailer": "^7.0.3", "rotating-file-stream": "^3.2.6", "sequelize": "^6.37.7", "sharp": "^0.34.3"}, "devDependencies": {"@fastify/swagger": "^9.5.1", "@fastify/swagger-ui": "^5.2.3", "eslint": "^9.29.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "nodemon": "^3.1.10", "prettier": "^3.5.3", "yaml": "^2.8.0"}, "lint-staged": {"**/*.{js,json}": ["prettier --write", "eslint --fix"]}}