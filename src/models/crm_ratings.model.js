import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const CrmRatings = sequelize.define(
    "CrmRatings",
    {
        rating_id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        country_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
            defaultValue: 0,
        },
        vendor_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        order_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        product_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: "CrmCustomer",
                key: "id",
            },
        },
        rating: {
            type: DataTypes.FLOAT,
            allowNull: true,
        },
        likes: {
            type: DataTypes.INTEGER,
            allowNull: false,
            defaultValue: [],
        },
        dislikes: {
            type: DataTypes.INTEGER,
            allowNull: false,
            defaultValue: [],
        },
        review_title: {
            type: DataTypes.STRING(200),
            allowNull: true,
        },
        review: {
            type: DataTypes.TEXT("long"),
            allowNull: true,
        },
        image: {
            type: DataTypes.JSON,
            allowNull: true,
        },
        rstatus: {
            type: DataTypes.TINYINT,
            allowNull: false,
            defaultValue: 0,
        },
        approved_by: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        reason: {
            type: DataTypes.STRING(255),
            allowNull: true,
        },
        is_edited: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false,
        },
        created_at: {
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW,
        },
        updated_at: {
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW,
            onUpdate: DataTypes.NOW,
        },
    },
    {
        tableName: "crm_ratings",
        timestamps: false,
    }
);

CrmRatings.associate = (models) => {
    CrmRatings.belongsTo(models.CrmCustomer, {
        foreignKey: "customer_id",
        targetKey: "id",
        as: "user",
    });

    CrmRatings.belongsTo(models.CatalogProduct, {
        foreignKey: "product_id",
        targetKey: "productid",
        as: "product",
    });
    CrmRatings.hasMany(models.CrmRatingVotes, {
        foreignKey: "rating_id",
        as: "votes",
    });
};

export default CrmRatings;
