import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const CrmRatings = sequelize.define(
    "CrmRatings",
    {
        ratings_id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        country_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
            defaultValue: 0,
        },
        vendor_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        orderno: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        productid: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: "CrmCustomer",
                key: "id",
            },
        },
        rating: {
            type: DataTypes.FLOAT,
            allowNull: true,
        },
        likes: {
            type: DataTypes.JSON,
            allowNull: false,
            defaultValue: [],
        },
        dislikes: {
            type: DataTypes.JSON,
            allowNull: false,
            defaultValue: [],
        },
        review_title: {
            type: DataTypes.STRING(200),
            allowNull: true,
        },
        review: {
            type: DataTypes.TEXT("long"),
            allowNull: true,
        },
        image: {
            type: DataTypes.JSON,
            allowNull: true,
        },
        rating_date: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        updated_on: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        updated_by: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        rstatus: {
            type: DataTypes.TINYINT,
            allowNull: false,
            defaultValue: 1,
        },
        approved_by: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        reason: {
            type: DataTypes.STRING(255),
            allowNull: true,
        },
        isEdited: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false,
        },
    },
    {
        tableName: "crm_ratings",
        timestamps: false,
    }
);

CrmRatings.associate = (models) => {
    CrmRatings.belongsTo(models.CrmCustomer, {
        foreignKey: "customer_id",
        targetKey: "id",
        as: "user",
    });

    CrmRatings.belongsTo(models.CatalogProduct, {
        foreignKey: "productid",
        targetKey: "productid",
        as: "product",
    });
};

export default CrmRatings;

`
CREATE TABLE crm_rating (
  rating_id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  country_id INT DEFAULT 0,
  vendor_id INT NOT NULL DEFAULT 0,
  productid INT NOT NULL,
  orderno INT DEFAULT NULL,
  customer_id INT NOT NULL,
  rating FLOAT DEFAULT 1,
  likes INT DEFAULT 0,
  review_title VARCHAR(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  review LONGTEXT COLLATE utf8mb4_unicode_ci,
  image JSON DEFAULT NULL,
  rating_date DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_on DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  rstatus TINYINT DEFAULT 1,
  approved_by INT DEFAULT NULL,
  reason VARCHAR(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  dislikes INT DEFAULT 0,
  is_edited TINYINT(1) NOT NULL DEFAULT 0
);

ALTER TABLE crm_rating
ADD INDEX idx_product_id (product_id),
ADD INDEX idx_customer_id (customer_id);
`;
