import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";
import { RATING_VOTES } from "../region/config.js";

const CrmRatingVotes = sequelize.define(
    "CrmRatingVotes",
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        rating_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        product_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        votes: {
            type: DataTypes.ENUM(RATING_VOTES.LIKE, RATING_VOTES.DISLIKE),
            allowNull: false,
            defaultValue: RATING_VOTES.LIKE,
        },
        created_at: {
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW,
        },
        updated_at: {
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW,
            onUpdate: DataTypes.NOW,
        },
    },
    {
        tableName: "crm_rating_reaction",
        timestamps: false,
    }
);

CrmRatingVotes.associate = (models) => {
    CrmRatingVotes.belongsTo(models.CrmRatings, {
        foreignKey: "rating_id",
        targetKey: "ratings_id",
        as: "rating",
    });

    CrmRatingVotes.belongsTo(models.CrmCustomer, {
        foreignKey: "customer_id",
        targetKey: "id",
        as: "user",
    });

    CrmRatingVotes.belongsTo(models.CatalogProduct, {
        foreignKey: "product_id",
        targetKey: "productid",
        as: "product",
    });
};

export default CrmRatingVotes;

`CREATE TABLE crm_rating_votes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    rating_id INT NOT NULL,
    customer_id INT NOT NULL,
    product_id INT NOT NULL,
    votes ENUM('like', 'dislike') NOT NULL DEFAULT 'like',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- Foreign Keys 
    CONSTRAINT fk_rating FOREIGN KEY (rating_id) REFERENCES crm_ratings(ratings_id) ON DELETE CASCADE;
    CONSTRAINT fk_customer FOREIGN KEY (customer_id) REFERENCES crm_customer(id) ON DELETE CASCADE;
    CONSTRAINT fk_product FOREIGN KEY (product_id) REFERENCES catalog_product(productid) ON DELETE CASCADE
    );

    -- Indexes
    ALTER TABLE crm_rating_votes
    ADD INDEX idx_rating_id (rating_id),
    ADD INDEX idx_customer_id (customer_id),
    ADD INDEX idx_product_id (product_id);

`;
