import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const CatalogAttribute = sequelize.define(
    "CatalogAttribute",
    {
        attributeid: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        product_type_id: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
        },
        attribute_name: {
            type: DataTypes.STRING(50),
            allowNull: true,
        },
        arabic_name: {
            type: DataTypes.STRING(50),
            allowNull: true,
        },
        category_ids: {
            type: DataTypes.TEXT("medium"), // corresponds to MEDIUMTEXT
            allowNull: true,
        },
        slug: {
            type: DataTypes.STRING(50),
            allowNull: true,
        },
        filter_flag: {
            type: DataTypes.TINYINT,
            defaultValue: 1,
        },
        update_date: {
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW,
        },
        rstatus: {
            type: DataTypes.TINYINT,
            defaultValue: 1,
        },
        os_attributeid: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
        },
    },
    {
        tableName: "catalog_attribute",
        timestamps: false,
    }
);

CatalogAttribute.associate = (models) => {
    CatalogAttribute.hasMany(models.CatalogAttributeValue, {
        foreignKey: "attributeid",
        as: "values",
    });
};

export default CatalogAttribute;
