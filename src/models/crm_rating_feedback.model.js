

const CrmRatingFeedback = sequelize.define(
    "CrmRatingFeedback",
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        rating_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        customer_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        product_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        reaction: {
            type: DataTypes.ENUM("like", "dislike"),
            allowNull: false,
            defaultValue: "like",
        },
        created_at: {
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW,
        },
        updated_at: {
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW,
            onUpdate: DataTypes.NOW,
        },
    },
    {
        tableName: "crm_rating_reaction",
        timestamps: false,
    }
);

CrmRatingFeedback.associate = (models) => {
    CrmRatingFeedback.belongsTo(models.CrmRatings, {
        foreignKey: "rating_id",
        targetKey: "ratings_id",
        as: "rating",
    });

    CrmRatingFeedback.belongsTo(models.CrmCustomer, {
        foreignKey: "customer_id",
        targetKey: "id",
        as: "user",
    });

    CrmRatingFeedback.belongsTo(models.CatalogProduct, {
        foreignKey: "product_id",
        targetKey: "productid",
        as: "product",
    });
};

export default CrmRatingFeedback;

`CREATE TABLE crm_rating_feedback (
    id INT AUTO_INCREMENT PRIMARY KEY,
    rating_id INT NOT NULL,
    customer_id INT NOT NULL,
    product_id INT NOT NULL,
    reaction ENUM('like', 'dislike') NOT NULL DEFAULT 'like',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- Foreign Keys
    CONSTRAINT fk_rating FOREIGN KEY (rating_id) REFERENCES crm_ratings(ratings_id) ON DELETE CASCADE,
    CONSTRAINT fk_customer FOREIGN KEY (customer_id) REFERENCES crm_customer(id) ON DELETE CASCADE,
    CONSTRAINT fk_product FOREIGN KEY (product_id) REFERENCES catalog_product(productid) ON DELETE CASCADE
);
`;
