// src/models/admin_vendor.model.js
import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const AdminVendor = sequelize.define(
    "AdminVendor",
    {
        vendor_id: {
            type: DataTypes.INTEGER.UNSIGNED,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false,
        },

        business_name: {
            type: DataTypes.STRING(255),
            allowNull: true,
        },

        contact_person: {
            type: DataTypes.STRING(50),
            allowNull: true,
        },

        contact_phone: {
            type: DataTypes.STRING(50),
            allowNull: true,
        },

        // DB column is "emaiid" (typo) — expose as "email" in code
        email: {
            type: DataTypes.STRING(100),
            allowNull: true,

            validate: {
                isEmailOrEmpty(value) {
                    if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                        throw new Error("Invalid email format");
                    }
                },
            },
        },

        business_type: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },

        vatid: {
            type: DataTypes.STRING(100),
            allowNull: true,
        },

        pan: {
            type: DataTypes.STRING(45),
            allowNull: true,
        },

        business_address: {
            type: DataTypes.TEXT("medium"),
            allowNull: true,
        },

        bank_details: {
            type: DataTypes.TEXT("medium"),
            allowNull: true,
        },

        // MEDIUMTEXT with JSON get/set
        kyc_files: {
            type: DataTypes.TEXT("medium"),
            allowNull: true,
            get() {
                const raw = this.getDataValue("kyc_files");
                if (!raw) return null;
                try {
                    return JSON.parse(raw);
                } catch {
                    return raw; // keep legacy non-JSON if any
                }
            },
            set(val) {
                if (val === null || val === undefined) {
                    this.setDataValue("kyc_files", null);
                } else {
                    this.setDataValue("kyc_files", JSON.stringify(val));
                }
            },
        },

        category_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },

        approved_by: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },

        approved_on: {
            type: DataTypes.DATE,
            allowNull: true,
            defaultValue: DataTypes.NOW, // CURRENT_TIMESTAMP
        },

        rstatus: {
            type: DataTypes.TINYINT,
            allowNull: true,
            defaultValue: 1,
        },
    },
    {
        tableName: "admin_vendor",
        timestamps: false,
    }
);

export default AdminVendor;
