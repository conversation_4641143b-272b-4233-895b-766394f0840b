const crm_rating_reaction = (sequelize, DataTypes) => {
    const CrmRatingLike = sequelize.define(
        "CrmRatingReaction",
        {
            id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            rating_id:{
                type: DataTypes.INTEGER,
                allowNull: false,
            }
        },
        {
            tableName: "crm_rating_reaction",
            timestamps: tratings_idrue,
        }
    );
    return CrmRatingLike;
};

export default crm_rating_reaction;
