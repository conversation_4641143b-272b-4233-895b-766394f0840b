import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js"; // Adjust path if needed

const getCMSSubCategoryModel = (countryId) => {
  const tableMap = {
    2: "oman_cms_subcategory",
    3: "qatar_cms_subcategory",
    5: "kuwait_cms_subcategory",
    6: "bahrain_cms_subcategory",
  };

  const tableName = tableMap?.[countryId] || "ourshopee_subcategory";

  return sequelize.define("cms_subcategory", {
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true,
    },
    cms_subcategory_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    category_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING(275),
      allowNull: false,
    },
    name_arabic: {
      type: DataTypes.STRING(150),
      allowNull: false,
    },
    position: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    url: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    link: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    mobile_link: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    seo_title: {
      type: DataTypes.STRING(80),
      allowNull: false,
    },
    seo_description: {
      type: DataTypes.STRING(210),
      allowNull: false,
    },
    seo_keywords: {
      type: DataTypes.STRING(210),
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    dealer_percentage: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    app_icon: {
      type: DataTypes.STRING(225),
      allowNull: false,
    },
    arabic_app_icon: {
      type: DataTypes.STRING(225),
      allowNull: false,
    },
    arabic_image: {
      type: DataTypes.STRING(225),
      allowNull: false,
    },
    first_parameter: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    last_parameter: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    front_view: {
      type: DataTypes.TINYINT,
      allowNull: false,
    },
    image: {
      type: DataTypes.STRING(225),
      allowNull: false,
    },
    banner: {
      type: DataTypes.STRING(225),
      allowNull: false,
    },
    banner_link: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    arabic_banner: {
      type: DataTypes.STRING(225),
      allowNull: false,
    },
    arabic_banner_link: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    tabby_type: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    cashew_type: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    tamara_type: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    search_priority: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
    },
    home_page: {
      type: DataTypes.TINYINT,
      allowNull: false,
    },
    status: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 1,
    },
  }, {
    tableName,
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: ["id"],
      },
    ],
  });
};

export default getCMSSubCategoryModel;
