// ESM
import { DataTypes } from "sequelize";
import sequelize from "../db/sequelize.js";

const AdminVendorLocation = sequelize.define(
    "AdminVendorLocation",
    {
        id: {
            type: DataTypes.INTEGER.UNSIGNED,
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
        },
        country_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        vendor_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        emirate_id: {
            type: DataTypes.STRING(150),
            allowNull: true,
        },
        area_id: {
            type: DataTypes.STRING(250),
            allowNull: true,
        },
        rstatus: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
    },
    {
        tableName: "admin_vendor_location",
        timestamps: false,

        indexes: [
            // mirrors your schema intent; PK already covers id uniqueness
            { fields: ["vendor_id"] },
            { fields: ["country_id"] },
        ],
    }
);

export default AdminVendorLocation;
