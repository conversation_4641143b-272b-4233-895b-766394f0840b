import Fastify from "fastify";
import cors from "@fastify/cors";
import fastifyFormbody from "@fastify/formbody";
import dotenv from "dotenv";
import Ajv from "ajv";
import ajvFormats from "ajv-formats";
import ajvErrors from "ajv-errors";
import fastifyMultipart from "@fastify/multipart";

import { getCorsOptions } from "./middlewares/corsOptions.middlewares.js";
import { createCountryLogger } from "./utils/logger.js";
import helmet from "./middlewares/helmet.middleware.js";

// Initialize routes
import healthcheckRoutes from "./routes/healthcheck.routes.js";
import authRoutes from "./routes/auth.routes.js";
import brandRoutes from "./routes/brand.routes.js";
import masterRoutes from "./routes/master.routes.js";
import homeRoutes from "./routes/home.routes.js";
import categoryRoutes from "./routes/category.routes.js";
import subCategoryRoutes from "./routes/subCategory.routes.js";
import sectionRoutes from "./routes/section.routes.js";
import addressRoutes from "./routes/address.routes.js";
import profileRoutes from "./routes/profile.routes.js";
import searchRoutes from "./routes/search.routes.js";
import productRoutes from "./routes/product.routes.js";
import orderRoutes from "./routes/orders.routes.js";
import paymentRoutes from "./routes/payment.routes.js";
import cartRoutes from "./routes/cart.routes.js";
import vendorRoutes from "./routes/vendor.routes.js";

import elasticsearch from "./config.js";

import { uploadFile } from "./middlewares/s3.middlewares.js";
import reviewRoutes from "./routes/reviews.routes.js";
import { COUNTRY_NAMES } from "./region/config.js";

dotenv.config({
    path: "./.env",
});

export const fastify = Fastify({
    logger: false,
});

await fastify.register(fastifyMultipart);

// Register CORS middleware with dynamic options
await fastify.register(fastifyFormbody);
await fastify.register(cors, getCorsOptions());
await fastify.register(helmet);

// Register Morgan-based logging middleware for all requests
const loggerMiddlewares = createCountryLogger();

// Add custom error message support
fastify.setValidatorCompiler(({ schema }) => {
    const ajv = new Ajv({
        allErrors: true, // REQUIRED by ajv-errors
        $data: true,
        useDefaults: true,
        coerceTypes: true,
    });
    ajvFormats(ajv);
    ajvErrors(ajv);
    return ajv.compile(schema);
});

// Capture request start time and handle country validation
fastify.addHook("onRequest", async (request, reply) => {
    // Capture request start time for logging
    request.raw.startTime = Date.now();
    const urlToBypassCheck = [
        "/api/health",
        "/api/payfortResponse",
        "/api/tabbyResponse",
    ];
    // Bypass country check for health check route
    const basePath = request.raw.url.split("?")[0];
    // console.log('urlllllalalalla', request.raw.url, basePath)
    if (urlToBypassCheck.includes(basePath)) {
        return;
    }

    const countryId = request.headers["country-id"];
    // console.log("countryId", countryId);
    const countries = Object.keys(COUNTRY_NAMES);
    if (!countries.includes(countryId)) {
        return reply.status(400).send({
            status: "failure",
            message: "Invalid or missing required header",
            data: {},
        });
    }

    request.country_id = countryId || 1;
});

// Capture request body after it's parsed
fastify.addHook("preHandler", async (request, reply) => {
    // Store request body for logging (after it gets processed by Fastify)
    if (request.body) {
        request.raw.requestBody = request.body;
    }
});

// Capture response payload for error logging
fastify.addHook("onSend", async (request, reply, payload) => {
    const countryId = request.headers["country-id"];
    request.country_id = countryId || 1;
    if (reply.statusCode >= 400) {
        try {
            const responseBody =
                typeof payload === "string" ? JSON.parse(payload) : payload;
            if (responseBody && responseBody.message) {
                reply.raw.locals = reply.raw.locals || {};
                reply.raw.locals.errorMessage = responseBody.message;
            }
        } catch (e) {
            // If parsing fails, use the raw payload
            reply.raw.locals = reply.raw.locals || {};
            reply.raw.locals.errorMessage =
                typeof payload === "string" ? payload : "Error response";
        }
    }
    return payload;
});

// Proper integration with Fastify using onResponse hook
fastify.addHook("onResponse", async (request, reply) => {
    // Morgan expects Express-like req/res objects
    const req = request.raw;
    const res = reply.raw;

    // Set additional properties for Morgan
    req.method = request.method;
    req.url = request.url;
    res.statusCode = reply.statusCode;

    // Make sure the request body is available for Morgan
    if (request.body && !req.requestBody) {
        req.requestBody = request.body;
    }

    // Execute each Morgan middleware
    loggerMiddlewares.forEach((middleware) => {
        middleware(req, res, () => {});
    });
});

fastify.setErrorHandler((error, request, reply) => {
    // Store error message for logging
    reply.raw.locals = reply.raw.locals || {};

    if (error.validation) {
        const firstError = error.validation[0];
        const message = firstError.message || "Invalid input";
        reply.raw.locals.errorMessage = `Validation Error: ${message}`;

        return reply.status(200).send({
            status: "failure",
            message,
            data: {},
        });
    }
    console.log(error);
    console.log("Error:", error.message);
    const NODE_ENV = process.env.NODE_ENV;
    const errorMessage =
        NODE_ENV === "development"
            ? error.message || "Unexpected error"
            : "Something went wrong while processing your request";

    reply.raw.locals.errorMessage = `Internal Error: ${error.message || "Unknown error"}`;

    return reply.status(500).send({
        status: "error",
        message: errorMessage,
        data: [],
    });
});
// REGISTER DB PLUGIN BEFORE ROUTES
await fastify.register(elasticsearch);
// Register healthcheck routes with `/api` prefix
await fastify.register(healthcheckRoutes, { prefix: "/api" });
await fastify.register(authRoutes, { prefix: "/api" });
await fastify.register(brandRoutes, { prefix: "/api" });
await fastify.register(masterRoutes, { prefix: "/api" });
await fastify.register(homeRoutes, { prefix: "/api" });
await fastify.register(categoryRoutes, { prefix: "/api" });
await fastify.register(subCategoryRoutes, { prefix: "/api" });
await fastify.register(sectionRoutes, { prefix: "/api" });
await fastify.register(addressRoutes, { prefix: "/api" });
await fastify.register(profileRoutes, { prefix: "/api" });
await fastify.register(searchRoutes, { prefix: "/api" });
await fastify.register(reviewRoutes, { prefix: "/api" });
await fastify.register(productRoutes, { prefix: "/api" });
await fastify.register(paymentRoutes, { prefix: "/api" });
await fastify.register(cartRoutes, { prefix: "/api" });
await fastify.register(orderRoutes, { prefix: "/api" });
await fastify.register(vendorRoutes, { prefix: "/api" });

// Test routes for logging verification
fastify.get("/api/test-access", async (request, reply) => {
    return {
        status: "success",
        message: "This is a test access log entry",
        timestamp: new Date().toISOString(),
        country: request.headers["country-id"] || "unknown",
    };
});

fastify.post("/api/test-error", async (request, reply) => {
    return reply.status(500).send({
        status: "error",
        message: "This is a test error log entry with request body",
        timestamp: new Date().toISOString(),
        country: request.headers["country-id"] || "unknown",
        receivedBody: request.body,
    });
});

fastify.post("/api/test-validation-error", async (request, reply) => {
    return reply.status(422).send({
        status: "error",
        message: "This is a test validation error log entry",
        timestamp: new Date().toISOString(),
        country: request.headers["country-id"] || "unknown",
        receivedBody: request.body,
    });
});

// Handle 404s
fastify.setNotFoundHandler((request, reply) => {
    const errorMessage = `Route ${request.raw.url} not found`;

    // Store error message for logging
    reply.raw.locals = reply.raw.locals || {};
    reply.raw.locals.errorMessage = errorMessage;

    return reply.code(404).send({
        status: "failure",
        message: errorMessage,
        data: {},
    });
});

export default fastify;
