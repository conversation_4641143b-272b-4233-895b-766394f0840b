import OmsOrders from "../models/oms_orders.model.js";
import { CURRENCY, VAT } from "../region/config.js";

/**
 * Generates the next order reference code by incrementing the current highest order reference code
 *
 * @returns {Promise<string>} The next order reference code (e.g., "A1001", "B2500", etc.)
 * @throws {Error} If database query fails or data validation fails
 *
 * @example
 * const refCode = await incrementOrderRefCode();
 * console.log(refCode); // "A1001", "B2500", etc.
 */
export const incrementOrderRefCode = async (country_id, lastRefCode = null) => {
    try {
        let currentRefCode = "";
        if (lastRefCode) {
            currentRefCode = lastRefCode;
        } else {
            // Fetch the latest order reference code from database
            const latestOrder = await OmsOrders.findOne({
                attributes: ["order_ref_code"],
                order: [["orderid", "DESC"]],
            });
            currentRefCode = latestOrder.order_ref_code || null;
        }

        currentRefCode = currentRefCode.split("-")[1];

        // Clean the reference code (remove any trailing suffixes)
        const cleanCode = currentRefCode.replace(/-\d+$/, "");

        const numbers = cleanCode.replace(/[^0-9]/g, "");
        const letters = cleanCode.replace(/[^a-zA-Z]/g, "");

        let ord_num, ord_alp;

        if (letters && numbers) {
            if (parseInt(numbers) > 10000) {
                // Increment alphabet part
                ord_alp = incrementLetters(letters);
                ord_num = 1001;
            } else {
                ord_num = parseInt(numbers) + 1;
                ord_alp = letters;
            }
        } else {
            if (parseInt(numbers) < 1001 || parseInt(numbers) > 10000) {
                ord_num = 1001;
            } else {
                ord_num = parseInt(numbers) + 1;
            }
            ord_alp = "A";
        }
        const country_code = CURRENCY[country_id].currency;

        return `${country_code}-${ord_alp}${ord_num}`;
    } catch (error) {
        throw new Error(
            `Failed to generate order reference code: ${error.message}`
        );
    }
};

/**
 * Increments a letter sequence like a base-26 counter (A→B, Z→AA, etc.)
 *
 * @param {string} letters - The letter string to increment
 * @returns {string} The incremented letter string in uppercase
 * @throws {Error} If input is invalid
 *
 * @example
 * incrementLetters("A") // returns "B"
 * incrementLetters("AZ") // returns "BA"
 */
export const incrementLetters = (str) => {
    str = str.toUpperCase();
    let carry = 1,
        result = "";

    for (let i = str.length - 1; i >= 0; i--) {
        let code = str.charCodeAt(i) - 65 + carry; // 'A' = 65
        carry = 0;

        if (code >= 26) {
            code -= 26;
            carry = 1;
        }

        result = String.fromCharCode(65 + code) + result;
    }

    if (carry) {
        result = "A" + result;
    }

    return result;
};

export const vatCalculator = (amount, countryId) => {
    return (amount * VAT[countryId]) / 100;

    //This will be correct VAT calculation
    // return (amount * VAT[country_id]) / (100 + VAT[country_id]);
};
