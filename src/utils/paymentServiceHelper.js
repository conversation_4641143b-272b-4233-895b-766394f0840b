import OmsOrders from "../models/oms_orders.model.js";
import { incrementOrderRefCode } from "./orderUtils.js";

/**
 * Creates child orders in bulk for better performance
 * @param {Object} vendorGroups - Grouped products by vendor
 * @param {Object} parentOrder - Parent order object
 * @returns {Array} Array of created child orders
 */
export const createChildOrdersBulk = async (vendorGroups, parentOrder) => {
    const vendorIds = Object.keys(vendorGroups).map((id) => parseInt(id));
    const childVendorIds = vendorIds.filter(
        (id) => id !== 0 && Object.keys(vendorGroups).length > 1
    );

    if (childVendorIds.length === 0) {
        return Promise.resolve([]); // No child orders needed
    }

    try {
        console.log(
            `Creating ${childVendorIds.length} child orders using bulkCreate`
        );

        // 1. Generate all reference codes upfront (still sequential due to business logic)
        const orderRefCodes = [];
        let lastRefCode = null;
        for (let i = 0; i < childVendorIds.length; i++) {
            const refCode = await incrementOrderRefCode(
                parentOrder.country_id,
                lastRefCode
            );
            lastRefCode = refCode;
            orderRefCodes.push(refCode);
        }

        // 2. Prepare bulk data for child orders
        const childOrdersData = childVendorIds.map((vendorId, index) => ({
            type: parentOrder.type,
            vendor_id: vendorId,
            country_id: parentOrder.country_id,
            order_ref_code: orderRefCodes[index],
            parent_orderid: parentOrder.orderid, // Set parent order ID
            customer_contact: parentOrder.customer_contact,
            order_date: parentOrder.order_date,
            promotionid: parentOrder.promotionid,
            promo_code: parentOrder.promo_code,
            discount_amount: 0,
            sub_total: 0,
            tax_amount: 0,
            shipping_charges: 0,
            processing_fee: 0,
            total_amount: 0,
            payment_method: parentOrder.payment_method,
            payment_methodid: parentOrder.payment_methodid,
            payment_status: parentOrder.payment_status,
            gway_paymentmethod: parentOrder.gway_paymentmethod,
            gway_order_id: parentOrder.gway_order_id,
            gway_transaction_id: parentOrder.gway_transaction_id,
            gway_status: parentOrder.gway_status,
            shippingaddress: parentOrder.shippingaddress,
            customer_details: parentOrder.customer_details,
            customerid: parentOrder.customerid,
            orderfrom: parentOrder.orderfrom,
            order_from_id: parentOrder.order_from_id,
            donation_fee: 0, // Only parent order gets donation fee
            order_statusid: 0, // Will be updated to 1 after confirmation
            order_status: "0",
        }));

        // 3. Bulk create all child orders
        const createdOrders = await OmsOrders.bulkCreate(childOrdersData, {
            returning: true, // Important: to get the generated orderids
        });

        console.log(
            `Successfully created ${createdOrders.length} child orders using bulkCreate`
        );
        return createdOrders;
    } catch (error) {
        console.error("Error in createChildOrdersBulk:", error);
        throw new Error(`Failed to create child orders: ${error.message}`);
    }
};
