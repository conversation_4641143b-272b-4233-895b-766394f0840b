// src/services/processMultipartAndUpload.js
import { Upload } from "@aws-sdk/lib-storage";
import path from "node:path";
import crypto from "node:crypto";
import sharp from "sharp";
import { S3Client } from "@aws-sdk/client-s3";

/**
 * Process a multipart Fastify request and upload images to S3.
 * - Uploads ORIGINAL + WEBP for each image
 * - Multiple fields, each can have single/multiple files
 * - One pass over req.parts()
 * - No ACL (compatible with "Bucket owner enforced")
 **/

// ---------- Config / helpers ----------
const ALLOWED_MIME = new Set([
    "image/jpeg",
    "image/png",
    "image/gif",
    "image/jpg",
    "image/heic",
    "image/heif",
    "image/webp",
    "image/bmp",
    "image/tiff",
    "image/svg+xml",
    "image/x-icon",
    // optionally: "image/avif",
]);

const s3 = new S3Client({
    region: process.env.BUCKET_REGION,
    credentials: {
        accessKeyId: process.env.S3_ACCESS_KEY,
        secretAccessKey: process.env.S3_SECRET_KEY,
    },
});

function sanitizeBaseName(name) {
    return path
        .basename(name || "file")
        .replace(/\s+/g, "-")
        .replace(/[^a-zA-Z0-9.-]/g, "")
        .toLowerCase();
}

function makeS3Key({ fieldName, originalName, prefix = "ourshopee-img" }) {
    const now = new Date();
    const safeBase = sanitizeBaseName(originalName);
    const rand = crypto.randomBytes(6).toString("hex");
    const yyyy = String(now.getFullYear());
    const mm = String(now.getMonth() + 1).padStart(2, "0");
    const dd = String(now.getDate()).padStart(2, "0");
    return `${prefix}/${fieldName}-${yyyy}-${mm}-${dd}-${Date.now()}-${rand}-${safeBase}`;
}

function toWebpKey(originalKey) {
    // replace extension with .webp; if none, just append .webp
    const ext = path.extname(originalKey);
    if (!ext) return `${originalKey}.webp`;
    return originalKey.slice(0, -ext.length) + ".webp";
}

async function streamToBuffer(stream) {
    const chunks = [];
    for await (const chunk of stream) chunks.push(chunk);
    return Buffer.concat(chunks);
}

async function uploadBufferToS3({
    Bucket,
    Key,
    Body,
    ContentType,
    CacheControl,
}) {
    const uploader = new Upload({
        client: s3,
        params: {
            Bucket,
            Key,
            Body,
            ContentType,
            ...(CacheControl ? { CacheControl } : {}),
            // IMPORTANT: no ACL — bucket has ACLs disabled.
        },
        leavePartsOnError: false,
    });
    await uploader.done();
    return { Key };
}

function cdnUrlForKey(key) {
    // If bucket is private, this is just a path; serve via CloudFront (CDN_IMAGE_BASE_URL) or signed URLs.
    const base = process.env.CDN_IMAGE_BASE_URL; // e.g. https://cdn.ourshopee.com
    return `${base}${key}`;
}

// ---------- Main upload function ----------
async function uploadOriginalAndWebp({
    buffer,
    mimetype,
    originalName,
    fieldName,
    keyPrefix = "/ourshopee-img",
    cacheControl = "public, max-age=31536000, immutable",
}) {
    if (!process.env.BUCKET_NAME) throw new Error("BUCKET_NAME is not set");
    if (!process.env.BUCKET_REGION) throw new Error("BUCKET_REGION is not set");
    if (!ALLOWED_MIME.has(mimetype))
        throw new Error(`Invalid file type: ${mimetype}`);

    const Bucket = process.env.BUCKET_NAME;

    // 1) ORIGINAL
    const originalKey = makeS3Key({
        fieldName,
        originalName,
        prefix: keyPrefix,
    });
    await uploadBufferToS3({
        Bucket,
        Key: originalKey,
        Body: buffer,
        ContentType: mimetype,
        CacheControl: cacheControl,
    });

    // 2) WEBP
    let webpKey = toWebpKey(originalKey);
    try {
        const webpBuffer = await sharp(buffer)
            .rotate()
            .webp({ quality: 80 })
            .toBuffer();
        await uploadBufferToS3({
            Bucket,
            Key: webpKey,
            Body: webpBuffer,
            ContentType: "image/webp",
            CacheControl: cacheControl,
        });
    } catch (e) {
        // If conversion fails (e.g., unsupported input / missing codec), skip webp but keep original
        // Optionally log: console.warn("WEBP conversion failed:", e);
        webpKey = undefined;
    }

    return {
        original: originalKey,
        ...(webpKey ? { webp: webpKey } : {}),
        file_url: cdnUrlForKey(originalKey),
    };
}

// ---------- Exported: processMultipartAndUpload with WebP ----------
export const processMultipartAndUpload = async (req, options = {}) => {
    if (!req.isMultipart())
        throw new Error("Content-Type must be multipart/form-data");

    const {
        allowedFields,
        keyPrefix = "/ourshopee-img",
        maxFilesPerField = 10,
        maxTotalFiles = 50,
        concurrency = 3,
    } = options;

    const allowSet = allowedFields
        ? new Set(
              Array.isArray(allowedFields) ? allowedFields : [allowedFields]
          )
        : null;

    const filesByField = Object.create(null);
    const countsByField = Object.create(null);
    const nonFileFields = Object.create(null);
    let totalFiles = 0;

    // Simple concurrency limiter for the whole (buffer + S3) work
    let inFlight = 0;
    const waiters = [];
    const acquire = async () => {
        if (inFlight >= concurrency) await new Promise((r) => waiters.push(r));
        inFlight += 1;
    };
    const release = () => {
        inFlight -= 1;
        const next = waiters.shift();
        if (next) next();
    };

    const tasks = [];

    for await (const part of req.parts()) {
        if (part.file) {
            const field = part.fieldname;

            if (allowSet && !allowSet.has(field)) {
                // Drain to avoid hanging the connection
                for await (const _ of part.file) {
                }
                continue;
            }

            if (!filesByField[field]) filesByField[field] = [];
            if (!countsByField[field]) countsByField[field] = 0;

            if (countsByField[field] >= maxFilesPerField) {
                for await (const _ of part.file) {
                }
                throw new Error(
                    `Too many files for ${field}. Max ${maxFilesPerField} allowed.`
                );
            }
            if (totalFiles >= maxTotalFiles) {
                for await (const _ of part.file) {
                }
                throw new Error(
                    `Too many files in request. Max ${maxTotalFiles} allowed.`
                );
            }

            countsByField[field] += 1;
            totalFiles += 1;

            // Process each file within concurrency guard:
            const task = (async () => {
                await acquire();
                try {
                    // Read the uploaded file stream into memory (safe with your 10MB/file limit)
                    const buf = await streamToBuffer(part.file);

                    // Upload ORIGINAL + WEBP
                    const item = await uploadOriginalAndWebp({
                        buffer: buf,
                        mimetype: part.mimetype,
                        originalName: part.filename,
                        fieldName: field,
                        keyPrefix,
                    });

                    filesByField[field].push(item);
                } finally {
                    release();
                }
            })();

            tasks.push(task);
        } else {
            // Collect non-file fields if present
            nonFileFields[part.fieldname] = part.value;
        }
    }

    if (tasks.length === 0) throw new Error("No files provided");

    await Promise.all(tasks);

    return {
        filesByField,
        totals: { totalFiles, byField: countsByField },
        fields: nonFileFields,
    };
};
