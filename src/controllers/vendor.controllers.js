import { successResponse, errorResponse } from "../utils/response.js";
import {
    addVendorService,
    getAreasService,
    getLocationsService,
} from "../services/vendor.service.js";
import { primaryCategoryService } from "../services/common.service.js";
import { checkMobileOTP } from "../services/auth.service.js";
const addVendor = async (request, reply) => {
    try {
        const result = await addVendorService(request);

        return successResponse(
            reply,
            200,
            result.message,
            result.success,
            result.data
        );
    } catch (err) {
        console.error("Add vendor error", err.message);
        return errorResponse(reply, 500, err, [], "error", request);
    }
};

const primaryCategory = async (req, reply) => {
    try {
        const result = await primaryCategoryService(req);
        return successResponse(
            reply,
            200,
            result.message,
            result.success,
            result.data
        );
    } catch (err) {
        console.error("Add vendor error", err.message);
        return errorResponse(reply, 500, err, [], "error", request);
    }
};

const getLocations = async (request, reply) => {
    try {
        const result = await getLocationsService(
            request.country_id,
            request.query
        );
        console.log(
            result.statusCode,
            result.message,
            result.status,
            result.data
        );
        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.status,
            result.data
        );
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error");
    }
};

const getAreas = async (request, reply) => {
    try {
        const result = await getAreasService(request.country_id, request.query);

        return reply.code(result.statusCode).send({
            status: result.status,
            message: result.message,
            data: result.data,
        });
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error");
    }
};

const sendOtpAndEmail = async (request, reply) => {
    try {
        const result = await checkMobileOTP(request, "mobile", true);

        return successResponse(
            reply,
            result.statusCode,
            result.message,
            result.success,
            {}
        );
    } catch (err) {
        return errorResponse(reply, 500, err, [], "error", request);
    }
};

export { addVendor, primaryCategory, sendOtpAndEmail, getAreas, getLocations };
