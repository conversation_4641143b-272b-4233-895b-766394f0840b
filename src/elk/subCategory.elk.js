import { CURRENCY } from "../region/config.js";
import { getCategoryBannerService } from "../services/brand.service.js";
import { transformProductItem } from "../services/common.service.js";
import { getElasticProductTransform } from "./common.elk.js";
import { searchRedirect } from "./search.elk.js";

// filtered_items_elk

export const filteredItemsElk = async (req) => {
    try {
        const filter_items_list = req.body.filtered_items.filter(
            (item) => !item.title.startsWith("utm_")
        );

        const priceRangeQuery = req.body.hasOwnProperty("price_range")
            ? [
                  {
                      range: {
                          "inventory.special_price": {
                              gte: parseInt(req.body.price_range[0].min),
                          },
                      },
                  },
                  {
                      range: {
                          "inventory.special_price": {
                              lte: parseInt(req.body.price_range[0].max),
                          },
                      },
                  },
              ]
            : [];

        const inventoryMustQuery = [
            { term: { "inventory.rstatus": 1 } },
            { match_phrase: { "inventory.stock": "In stock" } },
        ];

        const nestedInventoryQuery = {
            nested: {
                path: "inventory",
                query: {
                    bool: {
                        must: [
                            {
                                term: {
                                    "inventory.country_id": req.country_id,
                                },
                            },
                            ...inventoryMustQuery,
                            ...priceRangeQuery,
                        ],
                    },
                },
                inner_hits: {
                    size: 1,
                    _source: {
                        includes: [
                            "inventory.inventory_id",
                            "inventory.country_id",
                            "inventory.quantity",
                            "inventory.selling_price",
                            "inventory.special_price",
                            "inventory.promotion_price",
                            "inventory.from_date",
                            "inventory.to_date",
                            "inventory.price",
                        ],
                    },
                },
            },
        };

        const dynamic_query = filter_items_list
            .filter((ele) =>
                [
                    "brands",
                    "colors",
                    "category",
                    "subcategory",
                    "subsubcategory",
                ].includes(ele.title.toLowerCase())
            )
            .map((ele) => {
                const map = {
                    brands: "brand_name",
                    colors: "color_name",
                    category: "category_id",
                    subcategory: "subcategory_id",
                    subsubcategory: "sub_sub_category_id",
                };
                const field = map[ele.title.toLowerCase()] || ele.title;

                return ["brand_name", "color_name"].includes(field)
                    ? { match: { [field]: ele.value } }
                    : { terms: { [field]: ele.value } };
            });

        const dynamic_query_attributes = filter_items_list
            .filter(
                (ele) =>
                    ![
                        "sortby",
                        "brand_name",
                        "color_name",
                        "subcategory_id",
                        "category_id",
                        "sub_sub_category_id",
                    ].includes(ele.title)
            )
            .map((ele) => ({
                nested: {
                    path: "attributes",
                    query: {
                        bool: {
                            must: [
                                { match: { "attributes.key": ele.title } },
                                { terms: { "attributes.value": ele.value } },
                            ],
                        },
                    },
                },
            }));

        const sort_by_exits = filter_items_list.filter(
            (ele) => ele.title === "sortby"
        );

        let sortby_query = [
            { position: { order: "asc" } },
            { updated_date: { order: "desc" } },
        ];

        if (sort_by_exits.length > 0) {
            const val = sort_by_exits[0].value;
            if (val === "Low to High") {
                sortby_query = [
                    { "inventory.special_price": { order: "asc" } },
                ];
            } else if (val === "High to Low") {
                sortby_query = [
                    { "inventory.special_price": { order: "desc" } },
                ];
            } else if (val === "new arrival") {
                sortby_query = [];
            }
        }

        const hasSearch = req.body.hasOwnProperty("searchString");
        const client = await req.server.elasticsearch;

        const baseMust = [
            ...dynamic_query,
            ...dynamic_query_attributes,
            nestedInventoryQuery,
        ];

        let dynamic_query_output;

        if (hasSearch) {
            const search_query = [
                {
                    bool: {
                        must: [
                            {
                                match_phrase: {
                                    stock: "In stock",
                                },
                            },
                            {
                                bool: {
                                    should: [
                                        {
                                            multi_match: {
                                                query: req.body.searchString,
                                                type: "bool_prefix",
                                                fields: [
                                                    "subcategory_name^4",
                                                    "name",
                                                    "name._2gram",
                                                    "name._3gram",
                                                    "sku",
                                                ],
                                            },
                                        },
                                        {
                                            fuzzy: {
                                                name: {
                                                    value: req.body
                                                        .searchString,
                                                    fuzziness: "AUTO",
                                                },
                                            },
                                        },
                                    ],
                                },
                            },
                        ],
                    },
                },
                ...(req.body.subcategory_id !== "search"
                    ? [{ match: { subcategory_id: req.body.subcategory_id } }]
                    : []),
            ];

            dynamic_query_output = {
                must: [...search_query, ...baseMust],
            };
        } else {
            dynamic_query_output = {
                must: baseMust,
            };
        }

        const searchParams = {
            index: process.env.ELASTIC_PRODUCT_INDEX,
            body: {
                from: req.body.page == "1" ? 0 : (req.body.page - 1) * 50,
                size: 50,
                _source: [
                    "id",
                    "sku",
                    "name",
                    "url",
                    "image",
                    "avg_rating",
                    "total_ratings",
                    "reviews",
                ],
                ...(sort_by_exits.length > 0 || !hasSearch
                    ? { sort: sortby_query }
                    : {}),
                query: hasSearch
                    ? {
                          function_score: {
                              query: {
                                  bool: dynamic_query_output,
                              },
                              functions: [
                                  {
                                      filter: {
                                          match: { subcategory_id: "205" },
                                      },
                                      weight: 5,
                                  },
                                  {
                                      filter: {
                                          match: { subcategory_id: "224" },
                                      },
                                      weight: 4.9,
                                  },
                                  {
                                      filter: {
                                          match: { subcategory_id: "212" },
                                      },
                                      weight: 4.8,
                                  },
                                  {
                                      filter: {
                                          match: { subcategory_id: "63" },
                                      },
                                      weight: 4.7,
                                  },
                                  {
                                      filter: {
                                          match: { subcategory_id: "206" },
                                      },
                                      weight: 3,
                                  },
                                  {
                                      filter: {
                                          match: { subcategory_id: "455" },
                                      },
                                      weight: 4.4,
                                  },
                              ],
                          },
                      }
                    : {
                          bool: dynamic_query_output,
                      },
            },
        };

        const result = await client.search(searchParams);
        return await getElasticProductTransform(result);
        // return result.hits.hits.map((h) => h._source);
    } catch (err) {
        throw err;
    }
};

export const filterProductsElk = async (request) => {
    try {
        const { subcat_url, sub_subcat_url, page = 1 } = request.query;
        const countryId = request.country_id;

        // console.log('mustQuery', JSON.stringify(mustQuery, null, 2))
        const client = await request.server.elasticsearch;
        const from = parseInt(page) === 1 ? 0 : (parseInt(page) - 1) * 20;

        const mustQuery = [
            { term: { status: 1 } },
            { term: { "subcategory_url.keyword": subcat_url } },
            ...(sub_subcat_url
                ? [{ term: { "sub_sub_category_url.keyword": sub_subcat_url } }]
                : []),
            {
                nested: {
                    path: "inventory",
                    query: {
                        bool: {
                            must: [
                                { term: { "inventory.country_id": countryId } },
                                { term: { "inventory.rstatus": 1 } },
                                {
                                    match_phrase: {
                                        "inventory.stock": "In stock",
                                    },
                                },
                            ],
                        },
                    },
                    inner_hits: {
                        size: 1,
                        _source: {
                            includes: [
                                "inventory.inventory_id",
                                "inventory.country_id",
                                "inventory.quantity",
                                "inventory.selling_price",
                                "inventory.special_price",
                                "inventory.promotion_price",
                                "inventory.from_date",
                                "inventory.to_date",
                                "inventory.price",
                            ],
                        },
                    },
                },
            },
        ];

        const searchBody = {
            _source: [
                "id",
                "sku",
                "name",
                "image",
                "url",
                "avg_rating",
                "total_ratings",
                "reviews",
            ],
            query: {
                bool: {
                    must: mustQuery,
                },
            },
            aggs: {
                max_price: {
                    nested: { path: "inventory" },
                    aggs: {
                        value: {
                            max: { field: "inventory.special_price" },
                        },
                    },
                },
                attributes: {
                    nested: { path: "attributes" },
                    aggs: {
                        key: {
                            terms: { field: "attributes.key" },
                            aggs: {
                                value: {
                                    terms: { field: "attributes.value" },
                                },
                            },
                        },
                    },
                },
                categories: {
                    terms: { field: "id" },
                    aggs: {
                        docs: {
                            top_hits: {
                                _source: [
                                    "subcategory_id",
                                    "sub_sub_category_id",
                                    "category_id",
                                    "category_name",
                                    "subcategory_name",
                                    "sub_sub_category_name",
                                ],
                                size: 1,
                            },
                        },
                    },
                },
                top_brands: {
                    terms: { field: "brand_id", size: 100000 },
                    aggs: {
                        docs: {
                            top_hits: {
                                _source: [
                                    "brand_id",
                                    "brand_name",
                                    "brand_image",
                                    "brand_url",
                                ],
                                size: 1,
                            },
                        },
                    },
                },
                colors: {
                    terms: { field: "color_id", size: 1000 },
                    aggs: {
                        docs: {
                            top_hits: {
                                _source: ["color_id", "color_name"],
                                size: 1,
                            },
                        },
                    },
                },
                brands: {
                    terms: { field: "brand_id", size: 1000 },
                    aggs: {
                        docs: {
                            top_hits: {
                                _source: ["brand_id", "brand_name"],
                                size: 1,
                            },
                        },
                    },
                },
            },
        };

        const response = await client.search({
            index: process.env.ELASTIC_PRODUCT_INDEX,
            from,
            size: 50,
            sort: [
                { position: { order: "asc" } },
                { updated_date: { order: "desc" } },
            ],
            body: searchBody,
        });

        const aggs = response.aggregations || {};

        const data = {
            filters: aggs.attributes?.key?.buckets || [],
            max_price: aggs.max_price?.value || 0,
            top_brands: aggs.top_brands?.buckets || [],
            colors: aggs.colors?.buckets || [],
            brands: aggs.brands?.buckets || [],
            products: await getElasticProductTransform(response),
            // products: response.hits?.hits?.map((h) => h._source) || [],
        };

        return data;
    } catch (err) {
        console.log("err", err);
        throw err;
        return err;
    }
};

// search_or_filtered_items
export const searchOrFilteredItems = async (req) => {
    try {
        const CDN_IMAGE_BASE_URL = process.env.CDN_IMAGE_BASE_URL;
        const currency = CURRENCY[req.country_id].currency;
        const subcategory_id = req.query.subcategory;
        const countryId = req.country_id;

        // Check if there's a search string before calling searchRedirect
        const searchString = req.query?.string || req.body?.searchString;
        const hasSearchString =
            searchString &&
            typeof searchString === "string" &&
            searchString.trim();

        let search_elk_redirect = [];
        if (hasSearchString) {
            search_elk_redirect = await searchRedirect(req);
        }

        // Handle search redirect
        if (search_elk_redirect.length > 10 && subcategory_id === "search") {
            return search_elk_redirect[0];
        }

        const elk_result = await merged_search_result_elk(req);

        let banners = [];

        if (elk_result.products[0]) {
            // console.log('elk_result.products[0]?.category_id', elk_result.products[0])
            banners = await getCategoryBannerService(
                countryId,
                elk_result.products[0]?.category_id
            );
        }

        const elk_normalized_data = (elk_result.categories || [])
            .map((cat) => {
                const cat_doc = cat.top_hit?.hits?.hits?.[0]?._source || {};

                const subcategories = (cat.subcategories?.buckets || [])
                    .map((sub) => {
                        const sub_doc =
                            sub.top_hit?.hits?.hits?.[0]?._source || {};
                        const sub_subcategories = (
                            sub.sub_subcategories?.buckets || []
                        )
                            .map((ssc) => {
                                const ssc_doc =
                                    ssc.top_hit?.hits?.hits?.[0]?._source || {};
                                if (ssc_doc.sub_sub_category_id) {
                                    return {
                                        sub_category_id: sub.key,
                                        value: `${ssc.key}_${sub.key}@subsubcategory`,
                                        sub_subcategory_id: ssc.key,
                                        label: ssc_doc.sub_sub_category_name,
                                    };
                                }
                                return null;
                            })
                            .filter(Boolean);

                        if (
                            sub_doc.subcategory_id &&
                            sub_doc.subcategory_name
                        ) {
                            return {
                                category_id: cat.key,
                                subcategory_id: sub.key,
                                value: `${sub.key}_${cat.key}@subcategory`,
                                label: sub_doc.subcategory_name,
                                children: sub_subcategories,
                            };
                        }
                        return null;
                    })
                    .filter(Boolean);

                if (cat_doc.category_id) {
                    return {
                        category_id: cat.key,
                        value: `${cat.key}@category`,
                        label: cat_doc.category_name,
                        children: subcategories,
                    };
                }
                return null;
            })
            .filter(Boolean);

        const colors = (elk_result.colors || [])
            .map((ele) => ({
                id: ele.docs.hits.hits[0]?._source?.color_id,
                name: ele.docs.hits.hits[0]?._source?.color_name,
            }))
            .filter((col) => col?.id && col.id !== 0);

        const brands = (elk_result.brands || [])
            .map((ele) => ({
                id: ele.docs.hits.hits[0]?._source?.brand_id,
                name: ele.docs.hits.hits[0]?._source?.brand_name,
            }))
            .filter((brand) => brand?.id && brand.id !== 0);

        const products = (elk_result.products || [])
            .map((ele) => {
                if (!ele) return null; // guard against undefined/null items

                const product = { ...ele };

                return {
                    brand_id: product.brand_id,
                    subcategory_id: product.subcategory_id,
                    ...transformProductItem(product, currency),
                    _score: product._score,
                };
            })
            .filter(Boolean); // remove nulls

        // console.log('products', products)
        return {
            filters: {
                checkbox: [
                    { id: 1, title: "Colors", list: colors },
                    { id: 2, title: "Brands", list: brands },
                ],
                categories: elk_normalized_data,
                slider_range: [
                    {
                        title: "price",
                        min_value: elk_result?.priceStats?.min || 0,
                        max_value: elk_result?.priceStats?.max || 4000,
                    },
                ],
            },
            display_items: {
                top_brands: [
                    // Static top brands can be abstracted to a constant if needed
                    {
                        id: 11,
                        brand_name: "Samsung",
                        url: "Samsung",
                        image: `${CDN_IMAGE_BASE_URL}/ourshopee-img/ourshopee_brands/677654546samsung.png`,
                    },
                    {
                        id: 7,
                        brand_name: "Apple",
                        url: "Apple",
                        image: `${CDN_IMAGE_BASE_URL}/ourshopee-img/ourshopee_brands/26204259310893584apple.png`,
                    },
                    {
                        id: 215,
                        brand_name: "Xiaomi",
                        url: "Xiaomi",
                        image: `${CDN_IMAGE_BASE_URL}/ourshopee-img/ourshopee_brands/674164445xiaomi.png`,
                    },
                    {
                        id: 25,
                        brand_name: "Oppo",
                        url: "Oppo",
                        image: `${CDN_IMAGE_BASE_URL}/ourshopee-img/ourshopee_brands/341608086oppo.png`,
                    },
                    {
                        id: 1219,
                        brand_name: "Honor",
                        url: "Honor",
                        image: `${CDN_IMAGE_BASE_URL}/ourshopee-img/ourshopee_brands/228429790honor.png`,
                    },
                    {
                        id: 14,
                        brand_name: "Nokia",
                        url: "Nokia",
                        image: `${CDN_IMAGE_BASE_URL}/ourshopee-img/ourshopee_brands/378696696nokia.png`,
                    },
                    {
                        id: 37,
                        brand_name: "Huawei",
                        url: "Huawei",
                        image: `${CDN_IMAGE_BASE_URL}/ourshopee-img/ourshopee_brands/946406479huawei.png`,
                    },
                    {
                        id: 43,
                        brand_name: "Motorola",
                        url: "Motorola",
                        image: `${CDN_IMAGE_BASE_URL}/ourshopee-img/ourshopee_brands/`,
                    },
                    {
                        id: 1037,
                        brand_name: "OnePlus",
                        url: "OnePlus",
                        image: `${CDN_IMAGE_BASE_URL}/ourshopee-img/ourshopee_brands/711109024Logo-Oneplus.png`,
                    },
                    {
                        id: 23,
                        brand_name: "Lava",
                        url: "Lava",
                        image: `${CDN_IMAGE_BASE_URL}/ourshopee-img/ourshopee_brands/`,
                    },
                    {
                        id: 1608,
                        brand_name: "Vivo",
                        url: "Vivo",
                        image: `${CDN_IMAGE_BASE_URL}/ourshopee-img/ourshopee_brands/197225333Vivo 01.png`,
                    },
                    {
                        id: 2129,
                        brand_name: "Realme",
                        url: "Realme",
                        image: `${CDN_IMAGE_BASE_URL}/ourshopee-img/ourshopee_brands/339554950realme 01.png`,
                    },
                    {
                        id: 1,
                        brand_name: "Lenovo",
                        url: "Lenovo",
                        image: `${CDN_IMAGE_BASE_URL}/ourshopee-img/ourshopee_brands/108871391lenovo.png`,
                    },
                    {
                        id: 1141,
                        brand_name: "BASEUS",
                        url: "BASEUS",
                        image: `${CDN_IMAGE_BASE_URL}/ourshopee-img/ourshopee_brands/437936864`,
                    },
                    {
                        id: 2007,
                        brand_name: "Redmi",
                        url: "Redmi",
                        image: `${CDN_IMAGE_BASE_URL}/ourshopee-img/ourshopee_brands/`,
                    },
                ],
                banners,
                products,
            },
        };
    } catch (err) {
        console.error(err.stack);
        throw err;
        return "error";
    }
};

export const merged_search_result_elk = async (req) => {
    const client = req.server.elasticsearch;
    const { string = "" } = req.query || {};
    let {
        subcategory_id,
        page = 0,
        filtered_items = [],
        price_range,
        searchString = "",
    } = req.body || {};

    filtered_items = filtered_items.filter(
        (item) => !item.title.startsWith("utm_")
    );

    const query = (string || searchString || "").trim();
    const queryTokens = query
        .toLowerCase()
        .split(/[\s\-]+/)
        .filter((t) => t.length > 0);

    const hasSearchQuery = query.length > 0;

    const size = 24;
    const from = (parseInt(page, 10) || 0) * size;

    // ------- helpers
    const getPriceRangeClauses = () => {
        const min = Number(price_range?.[0]?.min?.[0]);
        const max = Number(price_range?.[0]?.max?.[0]);
        const clauses = [];
        if (Number.isFinite(min))
            clauses.push({
                range: { "inventory.special_price": { gte: min } },
            });
        if (Number.isFinite(max))
            clauses.push({
                range: { "inventory.special_price": { lte: max } },
            });
        return clauses;
    };

    const inventoryFilterMust = [
        { term: { "inventory.country_id": req.country_id } },
        { term: { "inventory.rstatus": 1 } },
        { match_phrase: { "inventory.stock": "In stock" } },

        ...getPriceRangeClauses(),
    ];

    const FACET_MAP = {
        brands: { id: "brand_url", name: "brand_url.keyword" },
        brand: { id: "brand_url", name: "brand_url.keyword" },
        brand_name: { id: "brand_url", name: "brand_url.keyword" },
        colors: { id: "color_id", name: "color_name.keyword" },
        color: { id: "color_id", name: "color_name.keyword" },
        color_name: { id: "color_id", name: "color_name.keyword" },
        category: { id: "category_id", name: "category_name.keyword" },
        subcategory: { id: "subcategory_id", name: "subcategory_name.keyword" },
        subsubcategory: {
            id: "sub_sub_category_id",
            name: "sub_sub_category_name.keyword",
        },
    };

    const normalizeVals = (v) => (Array.isArray(v) ? v : v == null ? [] : [v]);

    const facetFilters = filtered_items
        .filter((f) => FACET_MAP[f?.title?.toLowerCase?.()])
        .map((f) => {
            const key = f.title.toLowerCase();
            const { id, name } = FACET_MAP[key];
            const raw = normalizeVals(f.value)
                .map((x) => (typeof x === "string" ? x.trim() : x))
                .filter((x) => x !== "" && x != null);
            const allNumeric = raw.every((x) => Number.isFinite(Number(x)));
            const field = allNumeric ? id : name;
            const finalVals = allNumeric ? raw.map(Number) : raw.map(String);
            return { terms: { [field]: finalVals } };
        });

    const facetAttrFilters = filtered_items
        .filter((f) => {
            const t = f?.title?.toLowerCase?.();
            return t && !FACET_MAP[t] && t !== "sortby";
        })
        .map((f) => ({
            nested: {
                path: "attributes",
                query: {
                    bool: {
                        must: [
                            { term: { "attributes.key": f.title } },
                            {
                                terms: {
                                    "attributes.value": normalizeVals(f.value),
                                },
                            },
                        ],
                    },
                },
            },
        }));

    const subcategoryParam = req.query?.subcategory ?? subcategory_id;
    const subcatFacet = (() => {
        if (!subcategoryParam || subcategoryParam === "search") return [];
        const last = String(subcategoryParam).split("_").pop();
        const isNum = Number.isFinite(Number(last));
        return isNum
            ? [{ term: { subcategory_id: Number(last) } }]
            : [{ term: { "subcategory_name.keyword": String(last) } }];
    })();

    // ===== Sort handling =====
    const sort_by = filtered_items.find((ele) => ele.title === "sortby");
    const sortby_query = sort_by
        ? sort_by.value[0] === "Low to High"
            ? [
                  {
                      "inventory.special_price": {
                          order: "asc",
                          nested: { path: "inventory" },
                      },
                  },
              ]
            : sort_by.value[0] === "High to Low"
              ? [
                    {
                        "inventory.special_price": {
                            order: "desc",
                            nested: { path: "inventory" },
                        },
                    },
                ]
              : sort_by.value[0] === "Position"
                ? [{ position: { order: "asc" } }]
                : sort_by.value[0] === "Position Desc"
                  ? [{ position: { order: "desc" } }]
                  : sort_by.value[0] === "New Arrival"
                    ? [{ updated_date: { order: "desc" } }]
                    : []
        : [];

    // Base must query used in all levels
    const coreMust = [
        { term: { status: 1 } },
        {
            nested: {
                path: "inventory",
                query: { bool: { must: inventoryFilterMust } },
                inner_hits: {
                    size: 1,
                    _source: {
                        includes: [
                            "inventory.inventory_id",
                            "inventory.country_id",
                            "inventory.price",
                            "inventory.special_price",
                            "inventory.selling_price",
                            "inventory.quantity",
                            "inventory.stock",
                            "inventory.from_date",
                            "inventory.to_date",
                            "inventory.promotion_price",
                        ],
                    },
                },
            },
        },
    ];

    const baseMustQuery = [
        ...coreMust,
        ...facetFilters,
        ...facetAttrFilters,
        ...subcatFacet,
    ];

    // -------------------- New multi-level search logic --------------------
    const levelQueries = {
        1: () => ({
            bool: {
                should: [
                    {
                        term: { "name.keyword": { value: query, boost: 1500 } },
                    },
                    {
                        term: {
                            "brand_name.keyword": { value: query, boost: 1000 },
                        },
                    },
                    {
                        term: {
                            "brand_url.keyword": { value: query, boost: 1000 },
                        },
                    },
                    { term: { sku: { value: query, boost: 700 } } },
                    {
                        term: {
                            "name.lower": {
                                value: query.toLowerCase(),
                                boost: 1400,
                            },
                        },
                    },
                    {
                        term: {
                            "brand_name.lower": {
                                value: query.toLowerCase(),
                                boost: 900,
                            },
                        },
                    },
                    {
                        term: {
                            "category_name.lower": {
                                value: query.toLowerCase(),
                                boost: 1500,
                            },
                        },
                    },
                    {
                        term: {
                            "subcategory_name.lower": {
                                value: query.toLowerCase(),
                                boost: 2000,
                            },
                        },
                    },
                    {
                        term: {
                            "sub_sub_category_name.lower": {
                                value: query.toLowerCase(),
                                boost: 1800,
                            },
                        },
                    },
                    {
                        match_phrase: {
                            name: {
                                query,
                                slop: queryTokens.length > 1 ? 3 : 0,
                                boost: 1200,
                            },
                        },
                    },
                    {
                        match_phrase: {
                            combined: {
                                query,
                                slop: queryTokens.length > 1 ? 5 : 0,
                                boost: 1000,
                            },
                        },
                    },
                    {
                        multi_match: {
                            query,
                            type: "cross_fields",
                            fields: [
                                "name.english^300",
                                "brand_name.english^200",
                                "brand_url.keyword^200",
                                "category_name.english^200",
                                "subcategory_name.english^250",
                                "sub_sub_category_name.english^350",
                            ],
                            operator: "and",
                            minimum_should_match: "100%",
                            boost: 2000,
                        },
                    },
                ],
                minimum_should_match: 1,
            },
        }),
        2: () => ({
            bool: {
                should: [
                    {
                        match: {
                            combined: {
                                query,
                                operator: queryTokens.length > 1 ? "and" : "or",
                                boost: 200,
                            },
                        },
                    },
                    {
                        multi_match: {
                            query,
                            type: "best_fields",
                            fields: [
                                "name.english^110",
                                "brand_name.english^90",
                                "brand_url.keyword^90",
                                "category_name.english^80",
                                "subcategory_name.english^90",
                                "sub_sub_category_name.english^80",
                                "seo_title.english^50",
                            ],
                            operator: "or",
                            minimum_should_match:
                                queryTokens.length > 1
                                    ? `${Math.ceil(queryTokens.length * 0.8)}`
                                    : "1",
                        },
                    },
                    ...queryTokens.map((token) => ({
                        bool: {
                            should: [
                                {
                                    match: {
                                        "name.english": {
                                            query: token,
                                            boost: 70,
                                        },
                                    },
                                },
                                {
                                    match: {
                                        "brand_name.english": {
                                            query: token,
                                            boost: 70,
                                        },
                                    },
                                },
                                {
                                    match: {
                                        "brand_url.keyword": {
                                            query: token,
                                            boost: 70,
                                        },
                                    },
                                },
                                {
                                    match: {
                                        "subcategory_name.english": {
                                            query: token,
                                            boost: 60,
                                        },
                                    },
                                },
                                {
                                    match: {
                                        "sub_sub_category_name.english": {
                                            query: token,
                                            boost: 50,
                                        },
                                    },
                                },
                                {
                                    match: {
                                        "category_name.english": {
                                            query: token,
                                            boost: 40,
                                        },
                                    },
                                },
                            ],
                        },
                    })),
                    {
                        multi_match: {
                            query,
                            type: "best_fields",
                            fuzziness: "AUTO",
                            prefix_length: 1,
                            fields: [
                                "name.english^50",
                                "brand_name.english^40",
                                "brand_url.keyword^40",
                                "subcategory_name.english^40",
                                "sub_sub_category_name.english^40",
                            ],
                            boost: 45,
                        },
                    },
                ],
                minimum_should_match: 1,
            },
        }),
        3: () => {
            if (query.length < 3) {
                return { bool: { must_not: { match_all: {} } } };
            }
            if (queryTokens.length === 1) {
                return {
                    bool: {
                        should: [
                            {
                                match: {
                                    "name.autocomplete": { query, boost: 8 },
                                },
                            },
                            {
                                match: {
                                    "brand_name.autocomplete": {
                                        query,
                                        boost: 6,
                                    },
                                },
                            },
                            {
                                match: {
                                    "brand_url.keyword": {
                                        query,
                                        boost: 6,
                                    },
                                },
                            },
                            {
                                match: {
                                    "category_name.autocomplete": {
                                        query,
                                        boost: 5,
                                    },
                                },
                            },
                            {
                                match: {
                                    "subcategory_name.autocomplete": {
                                        query,
                                        boost: 7,
                                    },
                                },
                            },
                            {
                                match: {
                                    "sub_sub_category_name.autocomplete": {
                                        query,
                                        boost: 6,
                                    },
                                },
                            },
                            {
                                fuzzy: {
                                    "name.lower": {
                                        value: query.toLowerCase(),
                                        fuzziness: "AUTO",
                                        prefix_length: 1,
                                        boost: 5,
                                    },
                                },
                            },
                            {
                                fuzzy: {
                                    "brand_name.lower": {
                                        value: query.toLowerCase(),
                                        fuzziness: "AUTO",
                                        prefix_length: 1,
                                        boost: 4,
                                    },
                                },
                            },
                            {
                                fuzzy: {
                                    "brand_url.keyword": {
                                        value: query.toLowerCase(),
                                        fuzziness: "AUTO",
                                        prefix_length: 1,
                                        boost: 4,
                                    },
                                },
                            },
                        ],
                    },
                };
            }
            return {
                bool: {
                    should: queryTokens.map((token) => ({
                        multi_match: {
                            query: token,
                            fields: [
                                "name.autocomplete^6",
                                "brand_name.autocomplete^5",
                                "brand_url.keyword^5",
                                "subcategory_name.autocomplete^4",
                                "sub_sub_category_name.autocomplete^4",
                            ],
                        },
                    })),
                    minimum_should_match: Math.max(
                        1,
                        Math.floor(queryTokens.length * 0.7)
                    ),
                },
            };
        },
        4: () => ({
            bool: {
                should: [
                    {
                        multi_match: {
                            query,
                            type: "most_fields",
                            fuzziness: "AUTO",
                            prefix_length: 1,
                            fields: [
                                "name^10",
                                "brand_name^9",
                                "brand_url.keyword^9",
                                "combined^7",
                                "subcategory_name^6",
                                "sub_sub_category_name^6",
                            ],
                        },
                    },
                    {
                        wildcard: {
                            "name.lower": {
                                value: `*${query.toLowerCase()}*`,
                                boost: 2,
                            },
                        },
                    },
                ],
                minimum_should_match: 1,
            },
        }),
    };

    const fallbackLevels = hasSearchQuery ? [1, 2, 3, 4] : [1];

    let result,
        matchedLevel = -1;
    for (const level of fallbackLevels) {
        const isSortedSearch = sortby_query.length > 0;

        const baseQuery = {
            bool: {
                must: baseMustQuery,
                ...(hasSearchQuery
                    ? {
                          should: levelQueries[level]().bool.should,
                          minimum_should_match: 1,
                      }
                    : {}),
            },
        };

        const searchBody = {
            size,
            from,
            _source: [
                "id",
                "name",
                "image",
                "url",
                "sku",
                "brand_id",
                "brand_name",
                "category_id",
                "category_name",
                "subcategory_id",
                "subcategory_name",
                "sub_sub_category_id",
                "sub_sub_category_name",
                "avg_rating",
                "total_ratings",
                "reviews",
            ],
        };

        if (hasSearchQuery && isSortedSearch) {
            searchBody.query = baseQuery;
            // Only sort by price, remove _score for strict price-based order
            searchBody.sort = sortby_query.map((sortField) => {
                const key = Object.keys(sortField)[0];
                const val = sortField[key];
                return {
                    [key]: {
                        order: val.order,
                        nested: val.nested,
                        mode: val.order === "asc" ? "min" : "max",
                    },
                };
            });
        } else if (hasSearchQuery && !isSortedSearch) {
            // relevance mode (with boosts / rescoring)
            searchBody.query = {
                function_score: {
                    query: baseQuery,
                    functions: [
                        {
                            filter: {
                                term: {
                                    "subcategory_name.keyword": "Mobile Phones",
                                },
                            },
                            weight: 11,
                        },
                        {
                            filter: {
                                term: { "subcategory_name.keyword": "Laptops" },
                            },
                            weight: 10,
                        },
                        {
                            filter: {
                                term: {
                                    "subcategory_name.keyword":
                                        "Desktop Computers",
                                },
                            },
                            weight: 7,
                        },
                        {
                            filter: {
                                term: { "category_name.keyword": "Pre Owned" },
                            },
                            weight: 6,
                        },
                    ],
                    boost_mode: "multiply",
                },
            };

            searchBody.rescore = [
                {
                    window_size: 30,
                    query: {
                        rescore_query: {
                            bool: {
                                should: [
                                    {
                                        term: {
                                            "name.keyword": {
                                                value: query,
                                                boost: 500,
                                            },
                                        },
                                    },
                                    {
                                        term: {
                                            "brand_name.keyword": {
                                                value: query,
                                                boost: 300,
                                            },
                                        },
                                    },
                                    {
                                        term: {
                                            "brand_url.keyword": {
                                                value: query,
                                                boost: 300,
                                            },
                                        },
                                    },
                                    {
                                        term: {
                                            "name.lower": {
                                                value: query.toLowerCase(),
                                                boost: 400,
                                            },
                                        },
                                    },
                                    {
                                        match_phrase: {
                                            name: {
                                                query,
                                                slop: 0,
                                                boost: 350,
                                            },
                                        },
                                    },
                                ],
                            },
                        },
                        query_weight: 1.0,
                        rescore_query_weight: 10.0,
                    },
                },
            ];
        } else {
            // No search query (just filters)
            searchBody.query = baseQuery;
        }

        result = await client.search({
            index: process.env.ELASTIC_PRODUCT_INDEX,
            body: searchBody,
        });

        if (result.hits?.hits?.length > 0 || !hasSearchQuery) {
            matchedLevel = level;
            break;
        }
    }

    if (!levelQueries[matchedLevel]) {
        matchedLevel = 1; // default to level 1 or appropriate fallback
    }

    const products = result.hits.hits.map((hit) => {
        const product = hit._source;
        const inventory =
            hit.inner_hits?.inventory?.hits?.hits?.[0]?._source || {};
        return { ...product, ...inventory, _score: hit._score };
    });

    // ---------- FACET AGGS
    const baseFacetQuery = {
        bool: {
            must: [
                { term: { status: 1 } },
                {
                    nested: {
                        path: "inventory",
                        query: { bool: { must: inventoryFilterMust } },
                    },
                },
            ],
            ...(hasSearchQuery
                ? {
                      should: levelQueries[matchedLevel]().bool.should,
                      minimum_should_match: 1,
                  }
                : {}),
        },
    };

    const brandsFacetFilter = {
        bool: {
            must: [baseFacetQuery],
        },
    };

    const colorsFacetFilter = {
        bool: {
            must: [baseFacetQuery],
        },
    };

    const categoriesFacetFilter = {
        bool: {
            must: [
                { term: { status: 1 } },
                {
                    nested: {
                        path: "inventory",
                        query: { bool: { must: inventoryFilterMust } },
                    },
                },
            ],
            should: hasSearchQuery
                ? [
                      // --- Boost exact brand name very high ---
                      {
                          term: {
                              "brand_name.keyword": {
                                  value: query,
                                  boost: 50000,
                              },
                          },
                      },
                      {
                          term: {
                              "brand_url.keyword": {
                                  value: query,
                                  boost: 50000,
                              },
                          },
                      },

                      // --- Boost exact product name high ---
                      {
                          term: {
                              "name.keyword": { value: query, boost: 3000 },
                          },
                      },

                      // --- Boost exact category name moderately ---
                      {
                          term: {
                              "category_name.keyword": {
                                  value: query,
                                  boost: 1500,
                              },
                          },
                      },

                      // Case-insensitive variants with lower boosts
                      {
                          term: {
                              "brand_name.lower": {
                                  value: query.toLowerCase(),
                                  boost: 40000,
                              },
                          },
                      },
                      {
                          term: {
                              "name.lower": {
                                  value: query.toLowerCase(),
                                  boost: 2500,
                              },
                          },
                      },
                      {
                          term: {
                              "category_name.lower": {
                                  value: query.toLowerCase(),
                                  boost: 1200,
                              },
                          },
                      },

                      // Phrase matches with moderate boosts
                      {
                          match_phrase: {
                              brand_name: { query, boost: 20000, slop: 0 },
                          },
                      },
                      {
                          match_phrase: {
                              sub_sub_category_name: {
                                  query,
                                  boost: 10000,
                                  slop: 0,
                              },
                          },
                      },
                      {
                          match_phrase: {
                              subcategory_name: { query, boost: 5000, slop: 0 },
                          },
                      },
                      {
                          match_phrase: {
                              name: { query, boost: 1500, slop: 0 },
                          },
                      },
                      // { match_phrase: { category_name: { query, boost: 1000, slop: 0 } } },
                  ]
                : [],
            minimum_should_match: hasSearchQuery ? 1 : 0,
        },
    };

    const aggsBody = {
        size: 0,
        aggs: {
            price: {
                nested: { path: "inventory" },
                aggs: {
                    stats: { stats: { field: "inventory.special_price" } },
                },
            },
            brands_facet: {
                filter: brandsFacetFilter,
                aggs: {
                    items: {
                        terms: { field: "brand_url.keyword", size: 1000 },
                        aggs: {
                            docs: {
                                top_hits: {
                                    size: 1,
                                    _source: [
                                        "brand_url.keyword",
                                        "brand_name",
                                    ],
                                },
                            },
                        },
                    },
                },
            },
            colors_facet: {
                filter: colorsFacetFilter,
                aggs: {
                    items: {
                        terms: { field: "color_id", size: 1000 },
                        aggs: {
                            docs: {
                                top_hits: {
                                    size: 1,
                                    _source: ["color_id", "color_name"],
                                },
                            },
                        },
                    },
                },
            },
            categories_facet: {
                filter: categoriesFacetFilter,
                aggs: {
                    items: {
                        terms: {
                            field: "category_id",
                            size: 50, // fetch enough for sorting
                        },
                        aggs: {
                            sum_score_per_category: {
                                sum: { script: "_score" },
                            },
                            max_score_per_category: {
                                max: { script: "_score" },
                            },
                            top_hit: {
                                top_hits: {
                                    size: 1,
                                    _source: ["category_id", "category_name"],
                                    sort: [{ _score: "desc" }],
                                },
                            },
                            subcategories: {
                                terms: { field: "subcategory_id", size: 100 },
                                aggs: {
                                    top_hit: {
                                        top_hits: {
                                            size: 1,
                                            _source: [
                                                "subcategory_id",
                                                "subcategory_name",
                                            ],
                                            sort: [{ _score: "desc" }],
                                        },
                                    },
                                    sub_subcategories: {
                                        terms: {
                                            field: "sub_sub_category_id",
                                            size: 100,
                                        },
                                        aggs: {
                                            top_hit: {
                                                top_hits: {
                                                    size: 1,
                                                    _source: [
                                                        "sub_sub_category_id",
                                                        "sub_sub_category_name",
                                                    ],
                                                    sort: [{ _score: "desc" }],
                                                },
                                            },
                                        },
                                    },
                                },
                            },
                            sort_and_limit: {
                                bucket_sort: {
                                    sort: [
                                        {
                                            max_score_per_category: {
                                                order: "desc",
                                            },
                                        },
                                    ],
                                    size: 8, // limit to top 8 purely by best score
                                },
                            },
                        },
                    },
                },
            },
        },
    };

    const aggregationResult = await client.search({
        index: process.env.ELASTIC_PRODUCT_INDEX,
        body: aggsBody,
    });

    const priceStats = aggregationResult.aggregations?.price?.stats ?? {};
    if (priceStats.min != null) priceStats.min = Math.floor(priceStats.min);
    if (priceStats.max != null) priceStats.max = Math.ceil(priceStats.max);

    return {
        total: result.hits?.total?.value ?? result.hits?.total ?? 0,
        products,
        brands:
            aggregationResult.aggregations?.brands_facet?.items?.buckets ?? [],
        colors:
            aggregationResult.aggregations?.colors_facet?.items?.buckets ?? [],
        categories:
            aggregationResult.aggregations?.categories_facet?.items?.buckets ??
            [],
        priceStats,
        matchedLevel,
    };
};
