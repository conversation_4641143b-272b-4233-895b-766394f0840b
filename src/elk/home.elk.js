import { configR<PERSON><PERSON>D<PERSON>, CURRENCY } from "../region/config.js";
import moment from "moment";
import {
    getElasticProductTransform,
    // getElasticResultProductTransform,
    getElasticResultProductTransformResult,
} from "./common.elk.js";
import { transformProductItem } from "../services/common.service.js";

// subcategory_items_elk
export const subcategoryItemsElk = async (req, fastify, countryId) => {
    try {
        if (req.query.sub_subcat_url) {
            var dynamic_query = [
                { match: { front_view: 1 } },
                { match: { stock: "In stock" } },
                { match: { status: 1 } },
                { range: { special_price: { gte: 0 } } },
                { range: { quantity: { gte: 0 } } },
                {
                    match: {
                        "sub_sub_category_url.keyword":
                            req.query.sub_subcat_url,
                    },
                },
            ];
        } else {
            var dynamic_query = [
                { match: { front_view: 1 } },
                { match: { stock: "In stock" } },
                { match: { status: 1 } },
                { range: { special_price: { gte: 0 } } },
                { range: { quantity: { gte: 0 } } },
                {
                    match: {
                        "subcategory_url.keyword": req.query.subcat_url,
                    },
                },
            ];
        }

        const client = fastify.elasticsearch;

        const result = await client.search({
            index: process.env.ELASTIC_PRODUCT_INDEX,
            body: {
                from: 0,
                size: 10,
                sort: [{ updated_date: { order: "desc" } }],
                query: {
                    bool: {
                        must: dynamic_query,
                    },
                },
            },
        });

        return result.hits.hits.map((h) => h._source);
    } catch (err) {
        throw err;
    }
};

// call_common_elk
export const callCommonElk = async (countryId, fastify) => {
    try {
        const exciting_offers = configRegionData[countryId].filter(
            (el) => el.type === "exciting_offers"
        );
        const deal_of_the_day = configRegionData[countryId].filter(
            (el) => el.type === "deal_of_the_day"
        );

        const now = new Date();

        const exciting_offers_datetime = moment
            .utc(moment(now).add(1, "days").format("YYYY-MM-DD HH:mm:ss"))
            .toISOString();

        const deal_of_the_Day_datetime = moment
            .utc(moment(now).format("YYYY-MM-DD HH:mm:ss"))
            .toISOString();

        const client = await fastify.elasticsearch;

        const result = await client.search({
            index: process.env.ELASTIC_PRODUCT_INDEX,
            body: {
                size: 0,
                query: {
                    bool: {
                        must: [
                            { term: { status: 1 } },
                            {
                                nested: {
                                    path: "inventory",
                                    query: {
                                        bool: {
                                            must: [
                                                {
                                                    term: {
                                                        "inventory.country_id":
                                                            countryId,
                                                    },
                                                },
                                                {
                                                    term: {
                                                        "inventory.rstatus": 1,
                                                    },
                                                },
                                                {
                                                    match_phrase: {
                                                        "inventory.stock":
                                                            "In stock",
                                                    },
                                                },

                                                {
                                                    range: {
                                                        "inventory.from_date": {
                                                            lte: deal_of_the_Day_datetime,
                                                        },
                                                    },
                                                },
                                            ],
                                        },
                                    },
                                    inner_hits: {
                                        size: 1,
                                        _source: {
                                            includes: [
                                                "inventory.inventory_id",
                                                "inventory.country_id",
                                                "inventory.quantity",
                                                "inventory.selling_price",
                                                "inventory.special_price",
                                                "inventory.promotion_price",
                                                "inventory.from_date",
                                                "inventory.to_date",
                                                "inventory.price",
                                            ],
                                        },
                                    },
                                },
                            },
                        ],
                    },
                },
                aggs: {
                    deal_of_the_day: {
                        filter: {
                            bool: {
                                must: [
                                    {
                                        term: {
                                            section_id:
                                                deal_of_the_day[0].section_id,
                                        },
                                    },
                                ],
                            },
                        },
                        aggs: {
                            docs: {
                                top_hits: {
                                    size: deal_of_the_day[0].limit,
                                    _source: {
                                        includes: [
                                            "id",
                                            "sku",
                                            "name",
                                            "image",
                                            "url",
                                            "section_id",
                                            "avg_rating",
                                            "total_ratings",
                                            "reviews",
                                        ],
                                    },
                                },
                            },
                        },
                    },
                    exciting_offers: {
                        filter: {
                            nested: {
                                path: "inventory",
                                query: {
                                    bool: {
                                        must: [
                                            {
                                                term: {
                                                    "inventory.country_id":
                                                        countryId,
                                                },
                                            },
                                            {
                                                term: {
                                                    "inventory.rstatus": 1,
                                                },
                                            },
                                            {
                                                match_phrase: {
                                                    "inventory.stock":
                                                        "In stock",
                                                },
                                            },

                                            {
                                                range: {
                                                    "inventory.offer_from": {
                                                        lte: exciting_offers_datetime,
                                                    },
                                                },
                                            },
                                            {
                                                range: {
                                                    "inventory.offer_to": {
                                                        gte: exciting_offers_datetime,
                                                    },
                                                },
                                            },
                                        ],
                                    },
                                },
                            },
                        },
                        aggs: {
                            docs: {
                                top_hits: {
                                    size: exciting_offers[0].limit,
                                    sort: [{ updated_date: { order: "desc" } }],
                                    _source: {
                                        includes: [
                                            "id",
                                            "sku",
                                            "name",
                                            "image",
                                            "url",
                                            "avg_rating",
                                            "total_ratings",
                                            "reviews",
                                        ],
                                    },
                                },
                            },
                        },
                    },
                },
            },
        });

        return {
            deal_of_the_day: getElasticResultProductTransformResult(
                result.aggregations.deal_of_the_day.docs.hits
            ),
            exciting_offers: getElasticResultProductTransformResult(
                result.aggregations.exciting_offers.docs.hits
            ),
        };
    } catch (err) {
        throw err;
    }
};

// call_common_elk1
export const bundleClearanceSaleELK = async (countryId, fastify) => {
    try {
        const clearance_sale = configRegionData[countryId].filter(
            (el) => el.type === "clearance_sale"
        );
        const bundle_deals = configRegionData[countryId].filter(
            (el) => el.type === "bundle_deals"
        );

        const client = fastify.elasticsearch;

        const result = await client.search({
            index: process.env.ELASTIC_PRODUCT_INDEX,
            body: {
                size: 0,
                query: {
                    bool: {
                        must: [
                            { term: { status: 1 } },
                            {
                                nested: {
                                    path: "inventory",
                                    query: {
                                        bool: {
                                            must: [
                                                {
                                                    term: {
                                                        "inventory.country_id":
                                                            countryId,
                                                    },
                                                },
                                                {
                                                    term: {
                                                        "inventory.rstatus": 1,
                                                    },
                                                },
                                                {
                                                    match_phrase: {
                                                        "inventory.stock":
                                                            "In stock",
                                                    },
                                                },
                                            ],
                                        },
                                    },
                                    inner_hits: {
                                        size: 1,
                                        _source: {
                                            includes: [
                                                "inventory.inventory_id",
                                                "inventory.country_id",
                                                "inventory.quantity",
                                                "inventory.selling_price",
                                                "inventory.price",
                                                "inventory.special_price",
                                                "inventory.promotion_price",
                                                "inventory.from_date",
                                                "inventory.to_date",
                                            ],
                                        },
                                    },
                                },
                            },
                        ],
                    },
                },
                aggs: {
                    clearance_sale: {
                        filter: {
                            bool: {
                                must: [
                                    {
                                        term: {
                                            section_id:
                                                clearance_sale[0].section_id,
                                        },
                                    },
                                ],
                            },
                        },
                        aggs: {
                            docs: {
                                top_hits: {
                                    size: clearance_sale[0].limit,
                                    _source: {
                                        includes: [
                                            "id",
                                            "sku",
                                            "name",
                                            "url",
                                            "image",
                                        ],
                                    },
                                },
                            },
                        },
                    },
                    bundle_deals: {
                        filter: {
                            bool: {
                                must: [
                                    {
                                        term: {
                                            section_id:
                                                bundle_deals[0].section_id,
                                        },
                                    },
                                ],
                            },
                        },
                        aggs: {
                            docs: {
                                top_hits: {
                                    size: bundle_deals[0].limit,
                                    _source: {
                                        includes: [
                                            "id",
                                            "sku",
                                            "name",
                                            "url",
                                            "image",
                                        ],
                                    },
                                },
                            },
                        },
                    },
                },
            },
        });

        return {
            clearance_sale: getElasticResultProductTransformResult(
                result.aggregations.clearance_sale.docs.hits
            ),
            bundle_deals: getElasticResultProductTransformResult(
                result.aggregations.bundle_deals.docs.hits
            ),
        };
    } catch (err) {
        throw err;
    }
};

// brand_week_elk
export const brandWeekElk = async (countryId, brand_id, fastify) => {
    try {
        const client = fastify.elasticsearch;

        const result = await client.search({
            index: process.env.ELASTIC_PRODUCT_INDEX,
            size: 20,
            _source: ["id", "sku", "name", "url", "image"],
            body: {
                query: {
                    bool: {
                        must: [
                            { term: { status: 1 } },
                            // { term: { front_view: 1 } },
                            { term: { brand_id } },
                            {
                                nested: {
                                    path: "inventory",
                                    query: {
                                        bool: {
                                            must: [
                                                {
                                                    term: {
                                                        "inventory.country_id":
                                                            countryId,
                                                    },
                                                },
                                                {
                                                    term: {
                                                        "inventory.rstatus": 1,
                                                    },
                                                },
                                                {
                                                    match_phrase: {
                                                        "inventory.stock":
                                                            "In stock",
                                                    },
                                                },
                                            ],
                                        },
                                    },
                                    inner_hits: {
                                        size: 1,
                                        _source: {
                                            includes: [
                                                "inventory.inventory_id",
                                                "inventory.country_id",
                                                "inventory.quantity",
                                                "inventory.selling_price",
                                                "inventory.price",
                                                "inventory.special_price",
                                                "inventory.promotion_price",
                                                "inventory.from_date",
                                                "inventory.to_date",
                                            ],
                                        },
                                        sort: [
                                            {
                                                price: {
                                                    order: "asc",
                                                },
                                            },
                                        ],
                                    },
                                },
                            },
                        ],
                    },
                },
            },
        });

        return getElasticProductTransform(result);
    } catch (err) {
        throw err;
    }
};

export const brandWeekElkChange = async (countryId, brand_id, fastify) => {
    try {
        const client = fastify.elasticsearch;

        const result = await client.search({
            index: process.env.ELASTIC_PRODUCT_INDEX,
            size: 5,
            _source: ["id", "sku", "name", "url", "image"],
            body: {
                query: {
                    bool: {
                        must: [
                            { term: { status: 1 } },
                            {
                                nested: {
                                    path: "inventory",
                                    query: {
                                        bool: {
                                            must: [
                                                {
                                                    term: {
                                                        "inventory.country_id":
                                                            countryId,
                                                    },
                                                },
                                                {
                                                    term: {
                                                        "inventory.rstatus": 1,
                                                    },
                                                },
                                                {
                                                    match_phrase: {
                                                        "inventory.stock":
                                                            "In stock",
                                                    },
                                                },
                                            ],
                                        },
                                    },
                                    inner_hits: {
                                        size: 1,
                                        _source: {
                                            includes: [
                                                "inventory.inventory_id",
                                                "inventory.country_id",
                                                "inventory.quantity",
                                                "inventory.selling_price",
                                                "inventory.price",
                                                "inventory.special_price",
                                                "inventory.promotion_price",
                                                "inventory.from_date",
                                                "inventory.to_date",
                                            ],
                                        },
                                        sort: [
                                            {
                                                "inventory.price": {
                                                    order: "asc",
                                                },
                                            },
                                        ],
                                    },
                                },
                            },
                        ],
                    },
                },
            },
        });

        return await getElasticProductTransform(result);
        // return result.hits.hits.map((hit) => hit._source);
    } catch (err) {
        throw err;
    }
};

// TopPicks_elk
export const topPicksElk = async (req, brand_id) => {
    try {
        const client = await req.server.elasticsearch;

        const result = await client.search({
            index: process.env.ELASTIC_PRODUCT_INDEX,
            _source: false,
            body: {
                query: {
                    bool: {
                        must: [
                            { term: { status: 1 } },
                            {
                                nested: {
                                    path: "inventory",
                                    query: {
                                        bool: {
                                            must: [
                                                {
                                                    term: {
                                                        "inventory.country_id":
                                                            req.country_id,
                                                    },
                                                },
                                                {
                                                    term: {
                                                        "inventory.rstatus": 1,
                                                    },
                                                },
                                                {
                                                    match_phrase: {
                                                        "inventory.stock":
                                                            "In stock",
                                                    },
                                                },

                                                {
                                                    range: {
                                                        "inventory.special_price":
                                                            { gte: 1 },
                                                    },
                                                },
                                            ],
                                        },
                                    },
                                    inner_hits: {
                                        name: "inventory_hit",
                                        size: 1,
                                        _source: [
                                            "inventory.inventory_id",
                                            "inventory.country_id",
                                            "inventory.quantity",
                                            "inventory.selling_price",
                                            "inventory.special_price",
                                            "inventory.promotion_price",
                                            "inventory.price",
                                            "inventory.from_date",
                                            "inventory.to_date",
                                        ],
                                        sort: [
                                            {
                                                "inventory.price": {
                                                    order: "asc",
                                                },
                                            },
                                        ],
                                    },
                                },
                            },
                            {
                                terms: {
                                    brand_id: Array.isArray(brand_id)
                                        ? brand_id
                                        : [brand_id],
                                },
                            },
                        ],
                    },
                },
                aggs: {
                    products: {
                        terms: {
                            field: "brand_id",
                        },
                        aggs: {
                            docs: {
                                top_hits: {
                                    size: 8,
                                    _source: {
                                        includes: [
                                            "id",
                                            "sku",
                                            "name",
                                            "url",
                                            "image",
                                            "brand_id",
                                            "brand_name",
                                        ],
                                    },
                                },
                            },
                        },
                    },
                },
            },
        });

        const buckets = result?.aggregations?.products?.buckets || [];

        return buckets;
    } catch (err) {
        throw err;
    }
};

// getDealOfTheDay_and_SaverZone_elk
export const getDealOfTheDayAndSaverZoneElk = async (data, req) => {
    try {
        const {
            section_id,
            from_date,
            quantity,
            special_price,
            offer_from,
            stock,
            offset = 0,
            limit = 10,
            type,
        } = data;

        const topLevelMust = [{ term: { status: 1 } }];

        const inventoryMust = [
            { term: { "inventory.country_id": req.country_id } },
            { term: { "inventory.rstatus": 1 } },
            { match_phrase: { "inventory.stock": "In stock" } },
        ];

        if (section_id) {
            topLevelMust.push({ term: { section_id } });
        }

        if (from_date && from_date != null) {
            topLevelMust.push({
                nested: {
                    path: "inventory",
                    query: {
                        range: {
                            "inventory.from_date": {
                                lte: moment.utc(from_date).toISOString(),
                            },
                        },
                    },
                },
            });
        }

        if (quantity) {
            inventoryMust.push({
                range: {
                    "inventory.quantity": { gte: quantity },
                },
            });
        }

        if (special_price) {
            inventoryMust.push({
                range: {
                    "inventory.special_price": { gte: special_price },
                },
            });
        }

        if (offer_from) {
            topLevelMust.push({
                nested: {
                    path: "inventory",
                    query: {
                        range: {
                            "inventory.offer_from": {
                                lte: moment.utc(offer_from).toISOString(),
                            },
                        },
                    },
                },
            });
        }

        if (stock) {
            inventoryMust.push({
                match_phrase: {
                    "inventory.stock": stock,
                },
            });
        }

        // Inject nested inventory query
        topLevelMust.push({
            nested: {
                path: "inventory",
                query: {
                    bool: {
                        must: inventoryMust,
                    },
                },
                inner_hits: {
                    _source: [
                        "inventory.inventory_id",
                        "inventory.country_id",
                        "inventory.quantity",
                        "inventory.selling_price",
                        "inventory.price",
                        "inventory.special_price",
                        "inventory.promotion_price",
                        "inventory.from_date",
                        "inventory.to_date",
                    ],
                    size: 1,
                },
            },
        });

        // Use only safe sort fields
        const sort =
            type === "saver"
                ? [
                      { offer_from: { order: "desc" } },
                      { updated_date: { order: "desc" } },
                  ]
                : [
                      { updated_date: { order: "desc" } },
                      { id: { order: "desc" } },
                  ];

        const client = await req.server.elasticsearch;

        const result = await client.search({
            index: process.env.ELASTIC_PRODUCT_INDEX,
            body: {
                from: offset,
                size: limit,
                _source: [
                    "id",
                    "sku",
                    "name",
                    "url",
                    "image",
                    "reviews",
                    "avg_rating",
                    "total_ratings",
                ],
                sort,
                query: {
                    bool: {
                        must: topLevelMust,
                    },
                },
            },
        });

        return (await getElasticProductTransform(result)) || [];
    } catch (err) {
        throw err;
    }
};

export const categoryItemsOneELK = async (req) => {
    const client = await req.server.elasticsearch;
    const random = Math.floor(Math.random() * (10 - 5 + 1)) + 5;

    const result = await client.search({
        index: process.env.ELASTIC_PRODUCT_INDEX,
        size: 0,
        body: {
            query: {
                bool: {
                    must: [
                        { term: { status: 1 } },
                        {
                            terms: {
                                "subcategory_name.keyword": [
                                    "Mobile Phones",
                                    "Laptops",
                                    "Television & Accessories",
                                    "Gaming Chairs & Desks",
                                    "Speakers & Audio Devices",
                                    "Food Preparation & Kitchenwares",
                                    "Perfumes",
                                    "Health Care Products",
                                    "Sports Nutrition",
                                    "Games",
                                ],
                            },
                        },
                        {
                            nested: {
                                path: "inventory",
                                inner_hits: {
                                    size: 1, // only one inventory record per product
                                    _source: {
                                        includes: [
                                            "inventory.inventory_id",
                                            "inventory.country_id",
                                            "inventory.quantity",
                                            "inventory.stock",
                                            "inventory.special_price",
                                            "inventory.price",
                                            "inventory.selling_price",
                                            "inventory.promotion_price",
                                            "inventory.from_date",
                                            "inventory.to_date",
                                        ],
                                    },
                                },
                                query: {
                                    bool: {
                                        must: [
                                            {
                                                term: {
                                                    "inventory.country_id":
                                                        req.country_id,
                                                },
                                            },
                                            {
                                                term: {
                                                    "inventory.rstatus": 1,
                                                },
                                            },

                                            {
                                                match_phrase: {
                                                    "inventory.stock":
                                                        "In stock",
                                                },
                                            },
                                            {
                                                range: {
                                                    "inventory.special_price": {
                                                        gte: 1,
                                                    },
                                                },
                                            },
                                        ],
                                    },
                                },
                            },
                        },
                    ],
                },
            },
            aggs: {
                subcategories: {
                    terms: {
                        field: "subcategory_id",
                        min_doc_count: 6,
                    },
                    aggs: {
                        subcategory_details: {
                            top_hits: {
                                size: 1,
                                _source: {
                                    includes: [
                                        "subcategory_id",
                                        "subcategory_name",
                                        "subcategory_url",
                                    ],
                                },
                            },
                        },
                        top_products: {
                            top_hits: {
                                size: 10,
                                _source: {
                                    includes: [
                                        "id",
                                        "name",
                                        "image",
                                        "url",
                                        "sku",
                                        "brand_id",
                                        "subcategory_id",
                                        "price",
                                    ],
                                },
                                sort: [{ updated_date: { order: "desc" } }],
                            },
                        },
                    },
                },
            },
        },
    });

    const currency = CURRENCY[req.country_id].currency;
    const finalData = await Promise.all(
        result.aggregations.subcategories.buckets.map(async (bucket) => {
            const subcategory = bucket.subcategory_details.hits.hits[0]._source;
            // console.log('bucket.top_products', JSON.stringify(bucket.top_products));
            const items = await getElasticProductTransform(bucket.top_products);

            const transFormProduct = items.map((item) => {
                return {
                    subcategory_id: item.subcategory_id,
                    brand_id: item.brand_id,
                    ...transformProductItem(item, currency),
                };
            });

            return {
                total: bucket.doc_count,
                subcategory_id: subcategory.subcategory_id,
                subcategory_name: subcategory.subcategory_name,
                url: subcategory.subcategory_url,
                items: transFormProduct,
            };
        })
    );

    return finalData;
};
