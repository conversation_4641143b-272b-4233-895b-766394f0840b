import { transformProductItem } from "../services/common.service.js";
import fastify from "../app.js";
import { CURRENCY } from "../region/config.js";

export const searchRelatedProducts = async (
    countryId,
    client,
    indexName,
    brand_id,
    subcategory_id,
    sku
) => {
    // console.log(
    //     ">>>>>>>>>>>>>>>>>>>>>>",
    //     countryId,
    //     indexName,
    //     brand_id,
    //     subcategory_id,
    //     sku
    // );
    const relatedResult = await client.search({
        index: indexName,
        body: {
            _source: [
                "id",
                "name",
                "image",
                "url",
                "sku",
                "avg_rating",
                "total_ratings",
                "reviews",
            ],
            size: 6,
            sort: [{ id: { order: "desc" } }],
            query: {
                bool: {
                    must: [
                        brand_id ? { term: { brand_id } } : null,
                        subcategory_id ? { term: { subcategory_id } } : null,
                        {
                            nested: {
                                path: "inventory",
                                query: {
                                    bool: {
                                        must: [
                                            {
                                                term: {
                                                    "inventory.country_id":
                                                        countryId,
                                                },
                                            },
                                            {
                                                term: {
                                                    "inventory.rstatus": 1,
                                                },
                                            },
                                            {
                                                match_phrase: {
                                                    "inventory.stock":
                                                        "In stock",
                                                },
                                            },
                                        ],
                                    },
                                },
                                inner_hits: {
                                    name: "inventory_hit",
                                    size: 1,
                                    _source: [
                                        "inventory.price",
                                        "inventory.selling_price",
                                        "inventory.special_price",
                                        "inventory.promotion_price",
                                        "inventory.from_date",
                                        "inventory.to_date",
                                    ],
                                    sort: [
                                        { "inventory.price": { order: "asc" } },
                                    ],
                                },
                            },
                        },
                    ].filter(Boolean),
                    must_not: {
                        bool: { must: [{ match: { sku: sku } }] },
                    },
                },
            },
        },
    });

    const currency = CURRENCY[countryId].currency;
    return relatedResult.hits.hits.map((h) => {
        const item = h._source;
        const inventory = h.inner_hits.inventory_hit.hits.hits[0]._source;
        const cleanedItem = { ...item, ...inventory };
        // console.log(cleanedItem);
        return transformProductItem(cleanedItem, currency);
    });
};

export const getProductsBygroupId = async (
    countryId,
    client,
    indexName,
    variantgroupid
) => {
    const relatedResult = await client.search({
        index: indexName,
        body: {
            _source: ["id", "name", "image", "url", "sku", "variant_code"],
            query: {
                bool: {
                    must: [
                        variantgroupid ? { term: { variantgroupid } } : null,

                        {
                            nested: {
                                path: "inventory",
                                query: {
                                    bool: {
                                        must: [
                                            {
                                                term: {
                                                    "inventory.country_id":
                                                        countryId,
                                                },
                                            },
                                            {
                                                term: {
                                                    "inventory.rstatus": 1,
                                                },
                                            },
                                            {
                                                match_phrase: {
                                                    "inventory.stock":
                                                        "In stock",
                                                },
                                            },
                                        ],
                                    },
                                },
                            },
                        },
                    ].filter(Boolean),
                },
            },
        },
    });

    const currency = CURRENCY[countryId].currency;
    return relatedResult.hits.hits.map((h) => {
        const item = h._source;
        return item;
    });
};

export const searchRecentlyViewed = async (
    countryId,
    client,
    indexName,
    skulist
) => {
    if (!skulist || skulist.length === 0) {
        return [];
    }

    const viewedResult = await client.search({
        index: indexName,
        body: {
            _source: [
                "id",
                "name",
                "image",
                "url",
                "sku",
                "avg_rating",
                "total_ratings",
                "reviews",
            ],
            size: 6,
            sort: [{ id: { order: "desc" } }],
            query: {
                bool: {
                    must: [
                        { terms: { sku: skulist } },

                        {
                            nested: {
                                path: "inventory",
                                query: {
                                    bool: {
                                        must: [
                                            {
                                                term: {
                                                    "inventory.country_id":
                                                        countryId,
                                                },
                                            },
                                            {
                                                term: {
                                                    "inventory.rstatus": 1,
                                                },
                                            },
                                            {
                                                match_phrase: {
                                                    "inventory.stock":
                                                        "In stock",
                                                },
                                            },
                                        ],
                                    },
                                },
                                inner_hits: {
                                    name: "inventory_hit",
                                    size: 1,
                                    _source: [
                                        "inventory.price",
                                        "inventory.selling_price",
                                        "inventory.special_price",
                                        "inventory.promotion_price",
                                        "inventory.from_date",
                                        "inventory.to_date",
                                    ],
                                    sort: [
                                        { "inventory.price": { order: "asc" } },
                                    ],
                                },
                            },
                        },
                    ],
                },
            },
        },
    });

    const currency = CURRENCY[countryId].currency;
    return viewedResult.hits.hits.map((h) => {
        const item = h._source;
        const inventory = h.inner_hits.inventory_hit.hits.hits[0]._source;
        const cleanedItem = { ...item, ...inventory };
        // console.log(cleanedItem);
        return transformProductItem(cleanedItem, currency);
    });
};

export const availableAttributesElk = async (
    countryId,
    id,
    array,
    type,
    nonArray,
    sizeId
) => {
    console.log("sizeId", sizeId);
    try {
        let dynamic_query;
        if (type === "colors") {
            dynamic_query = [
                { match: { variantgroupid: id } },
                { terms: { color_id: array } },
                { match: { storage_id: nonArray } },
                // { match: { size_id: sizeId } },
            ];
        } else if (type === "storage") {
            dynamic_query = [
                { match: { variantgroupid: id } },
                { match: { color_id: nonArray } },
                { terms: { storage_id: array } },
                // { terms: { size_id: sizeId } },
            ];
        } else if (type === "size") {
            console.log("array", array);
            dynamic_query = [
                { match: { variantgroupid: id } },
                // { match: { color_id: nonArray } },
                { terms: { size_id: array } },
            ];
        } else {
            return [];
        }
        const client = fastify.elasticsearch;

        const result = await client.search({
            index: process.env.ELASTIC_PRODUCT_INDEX,
            body: {
                _source: ["color_id", "storage_id", "url", "sku"],
                query: {
                    bool: {
                        must: dynamic_query,
                    },
                },
            },
        });
        return result.hits.hits.map((h) => h._source);
    } catch (err) {
        console.error("Error fetching available attributes from ELK:", err);
        return [];
    }
};
