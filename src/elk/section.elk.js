import { transformProductItem } from "../services/common.service.js";
import { CURRENCY } from "../region/config.js";

export const getSpecialDealsProductsELKOld = async (
    sectionId,
    limit = 10,
    offset = 0,
    req,
    get_special_deal_check_in_sec_id
) => {
    try {
        const client = await req.server.elasticsearch;
        const countryId = req.country_id; // Extract country_id like product.elk.js

        // Validate sectionId
        if (!sectionId || sectionId === "undefined" || sectionId === "null") {
            throw new Error("Section ID is required and must be a valid value");
        }

        // Convert to number and validate
        const numericSectionId = parseInt(sectionId);
        if (isNaN(numericSectionId)) {
            throw new Error("Section ID must be a valid number");
        }

        let sortCondition = {};

        if (get_special_deal_check_in_sec_id.includes(numericSectionId)) {
            sortCondition =
                limit === 12 ? { id: "desc" } : { offer_from: "desc" };
        } else {
            sortCondition = { from_date: "desc" };
        }

        const query = {
            index: process.env.ELASTIC_PRODUCT_INDEX,
            body: {
                from: offset,
                size: limit,
                _source: [
                    "id",
                    "sku",
                    "name",
                    "url",
                    "image",
                    "section_id",
                    "status",
                    "from_date",
                    "to_date",
                    "offer_from",
                    "offer_to",
                ], // Include product-level date fields
                query: {
                    bool: {
                        must: [
                            {
                                terms: {
                                    "section_id.keyword": [
                                        numericSectionId.toString(),
                                    ],
                                },
                            }, // Use keyword field for exact match
                            { term: { status: 1 } }, // Use term for exact match
                            {
                                nested: {
                                    path: "inventory",
                                    query: {
                                        bool: {
                                            must: [
                                                {
                                                    term: {
                                                        "inventory.country_id":
                                                            countryId,
                                                    },
                                                }, // Use extracted countryId without fallback
                                                {
                                                    term: {
                                                        "inventory.rstatus": 1,
                                                    },
                                                },
                                                {
                                                    match_phrase: {
                                                        "inventory.stock":
                                                            "In stock",
                                                    },
                                                },
                                            ],
                                        },
                                    },
                                    inner_hits: {
                                        name: "inventory_hit", // Named inner hits for consistency
                                        size: 1,
                                        _source: [
                                            "inventory.inventory_id",
                                            "inventory.country_id",
                                            "inventory.quantity",
                                            "inventory.selling_price",
                                            "inventory.special_price",
                                            "inventory.promotion_price",
                                            "inventory.from_date",
                                            "inventory.to_date",
                                            "inventory.offer_from",
                                            "inventory.offer_to",
                                            "inventory.price",
                                        ],
                                        sort: [
                                            {
                                                "inventory.price": {
                                                    order: "asc",
                                                },
                                            }, // Get cheapest inventory first
                                        ],
                                    },
                                },
                            },
                        ],
                    },
                },
                sort: [sortCondition],
            },
        };

        const result = await client.search(query);

        const currency = CURRENCY[countryId].currency; // Use extracted countryId
        return result.hits.hits.map((h) => {
            const item = h._source;
            const inventoryRaw =
                h.inner_hits.inventory_hit.hits.hits[0]._source;

            // Remove 'inventory.' prefix from inventory keys
            const inventory = {};
            for (const key in inventoryRaw) {
                if (Object.hasOwnProperty.call(inventoryRaw, key)) {
                    const cleanKey = key.startsWith("inventory.")
                        ? key.replace("inventory.", "")
                        : key;
                    inventory[cleanKey] = inventoryRaw[key];
                }
            }

            const cleanedItem = {
                ...item,
                ...inventory,
                // Handle dates more carefully - use null instead of undefined
                to_date: inventory.to_date || item.to_date || null,
                from_date: inventory.from_date || item.from_date || null,
                offer_to: inventory.offer_to || item.offer_to || null,
                offer_from: inventory.offer_from || item.offer_from || null,
            };

            return transformProductItem(cleanedItem, currency);
        });
    } catch (err) {
        throw err;
    }
};

export const getSpecialDealsProductsELK = async (
    sectionId,
    limit = 10,
    offset = 0,
    req,
    get_special_deal_check_in_sec_id = [],
    subCategoryId,
    subsubCategoryId,
    brandId
) => {
    try {
        const client = await req.server.elasticsearch;
        const countryId = req.country_id;

        const nSectionId = Number(sectionId);
        const hasSection = Number.isFinite(nSectionId) && nSectionId > 0;

        // ---- sort selection (unchanged logic) ----
        const sortCondition = get_special_deal_check_in_sec_id.includes(
            nSectionId
        )
            ? limit === 12
                ? { id: "desc" }
                : { offer_from: "desc" }
            : { from_date: "desc" };

        // ---- utility: choose correct field for section_id (toggle if needed) ----
        // If your mapping is array<number>: use "section_id"
        // If your mapping is keyword: use "section_id.keyword"
        const SECTION_FIELD = "section_id.keyword"; // <-- change to "section_id" if numeric array

        const filterClauses = [
            { term: { status: 1 } },
            {
                nested: {
                    path: "inventory",
                    query: {
                        bool: {
                            filter: [
                                { term: { "inventory.country_id": countryId } },
                                { term: { "inventory.rstatus": 1 } },
                                {
                                    match_phrase: {
                                        "inventory.stock": "In stock",
                                    },
                                },
                            ],
                        },
                    },
                    inner_hits: {
                        name: "inventory_hit",
                        size: 1,
                        _source: [
                            "inventory.inventory_id",
                            "inventory.country_id",
                            "inventory.quantity",
                            "inventory.selling_price",
                            "inventory.special_price",
                            "inventory.promotion_price",
                            "inventory.from_date",
                            "inventory.to_date",
                            "inventory.offer_from",
                            "inventory.offer_to",
                            "inventory.price",
                        ],
                        sort: [{ "inventory.price": { order: "asc" } }],
                    },
                },
            },
        ];

        // ---- conditional filters ----
        if (hasSection) {
            // If SECTION_FIELD is keyword, we must send string; for numeric field send number.
            const sectionValue = SECTION_FIELD.endsWith(".keyword")
                ? String(nSectionId)
                : nSectionId;

            // section_id can be single or multi-valued in ES; terms works for both.
            filterClauses.push({ terms: { [SECTION_FIELD]: [sectionValue] } });
        }

        if (Number.isFinite(subCategoryId) && subCategoryId > 0) {
            filterClauses.push({
                term: { subcategory_id: Number(subCategoryId) },
            });
        }

        if (Number.isFinite(subsubCategoryId) && subsubCategoryId > 0) {
            filterClauses.push({
                term: { sub_sub_category_id: Number(subsubCategoryId) },
            });
        }

        if (Number.isFinite(brandId) && brandId > 0) {
            filterClauses.push({ term: { brand_id: Number(brandId) } });
        }

        // console.log('filterClauses', JSON.stringify(filterClauses))
        const query = {
            index: process.env.ELASTIC_PRODUCT_INDEX,
            body: {
                from: offset,
                size: limit,
                _source: [
                    "id",
                    "sku",
                    "name",
                    "url",
                    "image",
                    "section_id",
                    "status",
                    "from_date",
                    "to_date",
                    "offer_from",
                    "offer_to",
                    "avg_rating",
                    "total_ratings",
                    "reviews",
                ],
                query: { bool: { filter: filterClauses } },
                sort: [sortCondition],
            },
        };

        const result = await client.search(query);
        const currency = CURRENCY[countryId].currency;

        return result.hits.hits.map((h) => {
            const item = h._source;
            const inventoryRaw =
                h.inner_hits.inventory_hit.hits.hits[0]._source;

            // flatten "inventory." keys
            const inventory = {};
            for (const k in inventoryRaw) {
                const ck = k.startsWith("inventory.")
                    ? k.slice("inventory.".length)
                    : k;
                inventory[ck] = inventoryRaw[k];
            }

            const merged = {
                ...item,
                ...inventory,
                // prefer inventory dates/offers when present
                to_date: inventory.to_date ?? item.to_date ?? null,
                from_date: inventory.from_date ?? item.from_date ?? null,
                offer_to: inventory.offer_to ?? item.offer_to ?? null,
                offer_from: inventory.offer_from ?? item.offer_from ?? null,
            };

            return transformProductItem(merged, currency);
        });
    } catch (err) {
        // bubble up with context
        // err.details = { sectionId, categoryId, subCategoryId, brandId };
        console.log("err", err);
        throw err;
    }
};

export const getComboDealsProductsELK = async (req, limit, from) => {
    try {
        const client = await req.server.elasticsearch;
        const countryId = req.country_id; // Extract country_id like product.elk.js

        // Validate sec_id
        if (
            !req.query.sec_id ||
            req.query.sec_id === "undefined" ||
            req.query.sec_id === "null"
        ) {
            throw new Error("Section ID is required and must be a valid value");
        }

        const numericSecId = parseInt(req.query.sec_id);
        if (isNaN(numericSecId)) {
            throw new Error("Section ID must be a valid number");
        }

        const result = await client.search({
            index: process.env.ELASTIC_PRODUCT_INDEX,
            body: {
                from: from,
                size: limit,
                _source: [
                    "id",
                    "sku",
                    "name",
                    "url",
                    "image",
                    "section_id",
                    "type_id",
                    "status",
                    "from_date",
                    "to_date",
                    "offer_from",
                    "offer_to",
                ], // Include product-level date fields
                query: {
                    bool: {
                        must: [
                            {
                                terms: {
                                    "section_id.keyword": [
                                        numericSecId.toString(),
                                    ],
                                },
                            },
                            {
                                term: {
                                    type_id: 2, // Combo deals - use numeric value
                                },
                            },
                            {
                                term: { status: 1 }, // Active products only
                            },
                            {
                                nested: {
                                    path: "inventory",
                                    query: {
                                        bool: {
                                            must: [
                                                {
                                                    term: {
                                                        "inventory.country_id":
                                                            countryId,
                                                    },
                                                }, // Use extracted countryId without fallback
                                                {
                                                    term: {
                                                        "inventory.rstatus": 1,
                                                    },
                                                },
                                                {
                                                    match_phrase: {
                                                        "inventory.stock":
                                                            "In stock",
                                                    },
                                                },
                                            ],
                                        },
                                    },
                                    inner_hits: {
                                        name: "inventory_hit",
                                        size: 1,
                                        _source: [
                                            "inventory.inventory_id",
                                            "inventory.country_id",
                                            "inventory.quantity",
                                            "inventory.selling_price",
                                            "inventory.special_price",
                                            "inventory.promotion_price",
                                            "inventory.from_date",
                                            "inventory.to_date",
                                            "inventory.offer_from",
                                            "inventory.offer_to",
                                            "inventory.price",
                                        ],
                                        sort: [
                                            {
                                                "inventory.price": {
                                                    order: "asc",
                                                },
                                            }, // Get cheapest inventory first
                                        ],
                                    },
                                },
                            },
                        ],
                    },
                },
                sort: [{ from_date: "desc" }], // Sort by newest combo deals first
            },
        });

        const currency = CURRENCY[countryId].currency; // Use extracted countryId
        return result.hits.hits.map((h) => {
            const item = h._source;
            const inventoryRaw =
                h.inner_hits.inventory_hit.hits.hits[0]._source;

            // Remove 'inventory.' prefix from inventory keys
            const inventory = {};
            for (const key in inventoryRaw) {
                if (Object.hasOwnProperty.call(inventoryRaw, key)) {
                    const cleanKey = key.startsWith("inventory.")
                        ? key.replace("inventory.", "")
                        : key;
                    inventory[cleanKey] = inventoryRaw[key];
                }
            }

            const cleanedItem = {
                ...item,
                ...inventory,
                // Use inventory dates if available, otherwise use product dates
                to_date: inventory.to_date || item.to_date,
                from_date: inventory.from_date || item.from_date,
                offer_to: inventory.offer_to || item.offer_to,
                offer_from: inventory.offer_from || item.offer_from,
            };
            return transformProductItem(cleanedItem, currency);
        });
    } catch (err) {
        throw err;
    }
};

export const eidResultELK = async (req) => {
    try {
        const countryId = req.country_id; // Extract country_id like product.elk.js

        // Note: sectionConfig needs to be imported or defined
        // For now, using a default empty array. You should populate this with actual SKU list
        const sku_list = []; // You'll need to define this based on your config

        if (!sku_list || sku_list.length === 0) {
            console.log("No SKU list provided for EID results");
            return [];
        }

        const client = await req.server.elasticsearch;
        const result = await client.search({
            index: process.env.ELASTIC_PRODUCT_INDEX,
            body: {
                _source: ["id", "sku", "name", "url", "image", "status"],
                query: {
                    bool: {
                        must: [
                            { terms: { sku: sku_list } },
                            { term: { status: 1 } },
                            {
                                nested: {
                                    path: "inventory",
                                    query: {
                                        bool: {
                                            must: [
                                                {
                                                    term: {
                                                        "inventory.country_id":
                                                            countryId,
                                                    },
                                                }, // Use extracted countryId without fallback
                                                {
                                                    term: {
                                                        "inventory.rstatus": 1,
                                                    },
                                                },
                                                {
                                                    match_phrase: {
                                                        "inventory.stock":
                                                            "In stock",
                                                    },
                                                },
                                            ],
                                        },
                                    },
                                    inner_hits: {
                                        name: "inventory_hit", // Named inner hits for consistency
                                        size: 1,
                                        _source: [
                                            "inventory.inventory_id",
                                            "inventory.country_id",
                                            "inventory.quantity",
                                            "inventory.selling_price",
                                            "inventory.special_price",
                                            "inventory.promotion_price",
                                            "inventory.from_date",
                                            "inventory.to_date",
                                            "inventory.offer_from",
                                            "inventory.offer_to",
                                            "inventory.price",
                                        ],
                                        sort: [
                                            {
                                                "inventory.price": {
                                                    order: "asc",
                                                },
                                            }, // Get cheapest inventory first
                                        ],
                                    },
                                },
                            },
                        ],
                    },
                },
            },
        });

        const currency = CURRENCY[countryId].currency; // Use extracted countryId
        return result.hits.hits.map((h) => {
            const item = h._source;
            const inventoryRaw =
                h.inner_hits.inventory_hit.hits.hits[0]._source;

            // Remove 'inventory.' prefix from inventory keys
            const inventory = {};
            for (const key in inventoryRaw) {
                if (Object.hasOwnProperty.call(inventoryRaw, key)) {
                    const cleanKey = key.startsWith("inventory.")
                        ? key.replace("inventory.", "")
                        : key;
                    inventory[cleanKey] = inventoryRaw[key];
                }
            }

            // Merge product and inventory data
            // Priority: inventory dates first, then fallback to product dates
            const cleanedItem = {
                ...item,
                ...inventory,
                // Use inventory dates if available, otherwise use product dates
                to_date: inventory.to_date || item.to_date,
                from_date: inventory.from_date || item.from_date,
                offer_to: inventory.offer_to || item.offer_to,
                offer_from: inventory.offer_from || item.offer_from,
            };
            return transformProductItem(cleanedItem, currency);
        });
    } catch (err) {
        console.error("Error in eidResultELK:", err);
        throw err;
    }
};

export const getInfinteScrollItemsELK = async (req) => {
    try {
        const client = await req.server.elasticsearch;
        const page = parseInt(req.query.page || "1", 10);
        const from = (page - 1) * 20;

        const inventoryMust = [
            { term: { "inventory.country_id": req.country_id } },
            { term: { "inventory.rstatus": 1 } },
            { match_phrase: { "inventory.stock": "In stock" } },
        ];

        let priceFilter = [];
        let subcategoryFilter = [];

        if (req.query.type == "205") {
            priceFilter = [
                {
                    nested: {
                        path: "inventory",
                        query: {
                            range: {
                                "inventory.price": {
                                    gte: 0,
                                    lte: 20,
                                },
                            },
                        },
                    },
                },
            ];
        } else {
            subcategoryFilter = [
                {
                    term: {
                        subcategory_id: 34,
                    },
                },
            ];
        }

        const result = await client.search({
            index: process.env.ELASTIC_PRODUCT_INDEX,
            body: {
                from,
                size: 20,
                _source: [
                    "id",
                    "sku",
                    "name",
                    "url",
                    "image",
                    "avg_rating",
                    "total_ratings",
                    "reviews",
                ],
                query: {
                    function_score: {
                        query: {
                            bool: {
                                must: [
                                    { term: { status: 1 } },
                                    ...priceFilter,
                                    ...subcategoryFilter,
                                    {
                                        nested: {
                                            path: "inventory",
                                            query: {
                                                bool: {
                                                    must: inventoryMust,
                                                },
                                            },
                                            inner_hits: {
                                                _source: [
                                                    "inventory.inventory_id",
                                                    "inventory.country_id",
                                                    "inventory.quantity",
                                                    "inventory.selling_price",
                                                    "inventory.special_price",
                                                    "inventory.promotion_price",
                                                    "inventory.from_date",
                                                    "inventory.to_date",
                                                    "inventory.price",
                                                ],
                                                size: 1,
                                            },
                                        },
                                    },
                                ],
                            },
                        },
                        random_score: {},
                    },
                },
            },
        });

        const currency = CURRENCY[req.country_id].currency;
        return result.hits.hits.map((h) => {
            const item = h._source;
            const inventoryRaw =
                h.inner_hits?.inventory?.hits?.hits?.[0]?._source || {};

            // Remove 'inventory.' prefix from inventory keys
            const inventory = {};
            for (const key in inventoryRaw) {
                if (Object.hasOwnProperty.call(inventoryRaw, key)) {
                    const cleanKey = key.startsWith("inventory.")
                        ? key.replace("inventory.", "")
                        : key;
                    inventory[cleanKey] = inventoryRaw[key];
                }
            }

            const cleanedItem = {
                ...item,
                ...inventory,
            };

            return transformProductItem(cleanedItem, currency);
        });
    } catch (err) {
        throw err;
    }
};
