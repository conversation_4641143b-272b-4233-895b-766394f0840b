export function pickSelectedOnlyUnique(alternateAttributes = [], opts = {}) {
    const { selectedSku, hideColorsWithoutStorage = true } = opts;

    // ----- helpers -----
    const lower = (s) => String(s ?? "").toLowerCase();
    const normStorage = (s) =>
        String(s || "")
            .replace(/\s+/g, "")
            .toUpperCase(); // "512GB", "2TB"
    const storageRank = (s) => {
        const v = normStorage(s);
        const tb = v.match(/^(\d+(?:\.\d+)?)TB$/);
        const gb = v.match(/^(\d+(?:\.\d+)?)GB$/);
        if (tb) return parseFloat(tb[1]) * 1024;
        if (gb) return parseFloat(gb[1]);
        return -1; // unknown -> lowest
    };

    const findSection = (pred) =>
        alternateAttributes.find((sec) => {
            const id = String(sec?.id ?? "");
            const title = String(sec?.Title ?? sec?.title ?? "");
            return pred({ id, title });
        });

    // Detect sections by id or title
    const storageSec = findSection(
        ({ id, title }) => id === "3" || /\bstorage\b/i.test(title)
    ) || { list: [] };
    const colorSec = findSection(
        ({ id, title }) => id === "1" || /\bcolou?r\b/i.test(title)
    ) || { list: [] };

    const storageList = Array.isArray(storageSec.list) ? storageSec.list : [];
    const colorList = Array.isArray(colorSec.list) ? colorSec.list : [];

    // ----- Build quick lookups -----
    // sku -> storage object (from storage section)
    const skuToStorage = new Map();
    for (const it of storageList) {
        if (!it?.sku) continue;
        skuToStorage.set(String(it.sku), {
            id: String(it.id ?? ""),
            name: String(it.name ?? ""),
            rank: storageRank(it.name),
            url: String(it.url ?? ""),
        });
    }

    // sku -> color object (from color section)
    const skuToColor = new Map();
    for (const it of colorList) {
        if (!it?.sku) continue;
        skuToColor.set(String(it.sku), {
            id: String(it.id ?? ""),
            name: String(it.name ?? ""),
            code: it.code ?? "",
            url: String(it.url ?? ""),
        });
    }

    // colorId -> [items in color section]
    const colorIdToItems = new Map();
    for (const it of colorList) {
        const colorId = String(it?.id ?? "");
        if (!colorIdToItems.has(colorId)) colorIdToItems.set(colorId, []);
        colorIdToItems.get(colorId).push(it);
    }

    // ----- Decide selected SKU (if any given, else first selected in input, else best in first color) -----
    let selected = null;

    // From explicit opts
    if (selectedSku) {
        selected = String(selectedSku);
    }

    // Or from any 'selected:1' in either section
    if (!selected) {
        const hit =
            storageList.find((x) => Number(x?.selected) === 1 && x?.sku)?.sku ||
            colorList.find((x) => Number(x?.selected) === 1 && x?.sku)?.sku;
        if (hit) selected = String(hit);
    }

    // Fallback: pick the highest storage of the first color that has any storage
    if (!selected) {
        // iterate colors in the order they appear
        for (const [colorId, items] of colorIdToItems) {
            // for this color, take all SKUs and pick the best storage
            let best = null;
            for (const it of items) {
                const st = skuToStorage.get(String(it.sku));
                if (!st) continue;
                if (!best || st.rank > best.rank)
                    best = { sku: String(it.sku), rank: st.rank };
            }
            if (best) {
                selected = best.sku;
                break;
            }
        }
        // if still nothing, fall back to the first color item (even if no storage mapping)
        if (!selected && colorList.length) {
            selected = String(colorList[0].sku);
        }
    }

    // Selected colorId (if we can resolve it)
    const selColor = selected ? skuToColor.get(selected) : null;
    const selectedColorId =
        selColor?.id ?? (colorList[0] ? String(colorList[0].id ?? "") : "");

    // ----- Build "Available Color" (unique per color) -----
    const colorOutput = [];
    const colorSeen = new Set(); // colorId

    for (const [colorId, items] of colorIdToItems) {
        // choose representative SKU for this color:
        // 1) if selected SKU belongs to this color -> use it
        // 2) else choose item with highest storage (using skuToStorage)
        // 3) else first item
        let rep = null;

        // #1 Prefer the selected SKU if same color
        if (selected && items.some((x) => String(x.sku) === selected)) {
            rep = items.find((x) => String(x.sku) === selected);
        }

        // #2 Otherwise best storage for that color
        if (!rep) {
            let best = null;
            for (const it of items) {
                const st = skuToStorage.get(String(it.sku));
                if (!st) continue;
                if (!best || st.rank > best.rank)
                    best = { item: it, rank: st.rank };
            }
            if (best) rep = best.item;
        }

        // #3 Fallback first item
        if (!rep) rep = items[0];

        if (hideColorsWithoutStorage) {
            // If requested to hide colors that have no storage mapping at all, skip
            const hasAnyStorage = items.some((x) =>
                skuToStorage.has(String(x.sku))
            );
            if (!hasAnyStorage) continue;
        }

        if (!colorSeen.has(colorId)) {
            colorSeen.add(colorId);
            colorOutput.push({
                code: rep.code ?? "",
                name: rep.name ?? "",
                id: String(rep.id ?? ""),
                sku: String(rep.sku ?? ""),
                url: String(rep.url ?? ""),
                selected: colorId === selectedColorId ? 1 : 0,
            });
        }
    }

    // Keep original color order (already in order via Map iteration) and ensure selected is consistent
    for (const c of colorOutput) {
        c.selected = c.id === selectedColorId ? 1 : 0;
    }

    // ----- Build "Available Storage" for the selected color only -----
    // Take all SKUs whose colorId equals selectedColorId, then uniq by storage name/id
    const storagesMap = new Map(); // normStorage(name) -> item

    // Find all items of the selected color
    const selectedColorItems =
        colorIdToItems.get(String(selectedColorId)) || [];
    for (const it of selectedColorItems) {
        const st = skuToStorage.get(String(it.sku));
        if (!st) continue;
        const key = `${st.id}|${normStorage(st.name)}`;
        if (!storagesMap.has(key)) {
            storagesMap.set(key, {
                code: "",
                name: st.name,
                id: st.id,
                sku: String(it.sku),
                url: st.url || String(it.url ?? ""),
                selected: selected && String(it.sku) === selected ? 1 : 0,
                _rank: st.rank,
            });
        } else {
            // if duplicate capacity points to a different SKU, prefer the selected, else prefer higher rank (no-op for same capacity)
            const cur = storagesMap.get(key);
            if (selected && String(it.sku) === selected) {
                cur.sku = String(it.sku);
                cur.url = st.url || String(it.url ?? "");
                cur.selected = 1;
            }
        }
    }

    const storageOutput = Array.from(storagesMap.values())
        .sort((a, b) => a._rank - b._rank)
        .map(({ _rank, ...rest }) => rest);

    // Ensure exactly one storage is selected
    if (!storageOutput.some((x) => x.selected === 1) && storageOutput.length) {
        storageOutput[0].selected = 1;
    }

    // ----- Final shape (color first) -----
    return {
        alternateAttributes: [
            { title: "Available Color", id: "1", list: colorOutput },
            { title: "Available Storage", id: "3", list: storageOutput },
        ],
    };
}
