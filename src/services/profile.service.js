import CrmCustomerWishlist from "../models/crm_customer_wishlist.model.js";
import CrmCustomer from "../models/crm_customer.model.js";

import { getProductsByIdsElk } from "../elk/common.elk.js";
import { CURRENCY } from "../region/config.js";

export const getUserWishlistService = async (req) => {
    try {
        const { user_id } = req.user;
        const currency = CURRENCY[req.country_id].currency;
        let productLists = [];
        let items = [];
        const products = await CrmCustomerWishlist.findAll({
            attributes: ["customer_id", "product_id"],
            where: { customer_id: user_id, rstatus: 1 },
            raw: true,
        });

        const productIds = products.map((product) => product.product_id);

        if (productIds.length) {
            productLists = await getProductsByIdsElk(req, productIds);

            items = productLists.map((item) => {
                return {
                    id: item.id,
                    name: item.name,
                    display_price: item.display_price,
                    currency,
                    countdown: item.countdown,
                    ...(item.country_id !== 3 && { old_price: item.price }),
                    image: item.image,
                    percentage: item.percentage,
                    url: item.url,
                    sku: item.sku,
                };
            });
        }
        let message = `Wish list fetched successfuly!`;

        return {
            statusCode: 200,
            message,
            status: "success",
            result: items,
        };
    } catch (err) {
        throw err;
    }
};

export const addAndRemoveWishListService = async (req) => {
    const { user_id } = req.user;
    const { product_id, sku } = req.body;

    if (!product_id || !sku) {
        return {
            statusCode: 400,
            message: "Missing product id or sku",
            status: "failure",
            result: {},
        };
    }

    let message = "";
    let result;

    let wishList = await CrmCustomerWishlist.findOne({
        where: { product_id, customer_id: user_id },
    });

    if (wishList) {
        // update entry
        const newStatus = wishList.rstatus === 1 ? 2 : 1;
        result = newStatus;

        await wishList.update({ rstatus: newStatus });

        message =
            newStatus === 1
                ? "Product added to wishlist!"
                : "Product removed from wishlist!";
    } else {
        // New entry
        await CrmCustomerWishlist.create({
            customer_id: user_id,
            product_id,
            sku,
            rstatus: 1,
        });

        result = 1;
    }

    return {
        statusCode: 200,
        message,
        status: "success",
        result,
    };
};

export const getMyProfileService = async (req) => {
    const { user_id } = req.user;

    const result = await CrmCustomer.findByPk(user_id, {
        attributes: [
            "id",
            "first_name",
            "last_name",
            "email",
            "mobile",
            "gender",
            "nationality",
            "rstatus",
            "vip",
        ],
        raw: true,
    });

    return {
        statusCode: 200,
        message: "message",
        status: "success",
        result: [result],
    };
};

export const updateMyProfileService = async (req) => {
    const { user_id } = req.user;
    const { first_name, last_name, email, mobile, gender, nationality } =
        req.body;

    const [updatedCount] = await CrmCustomer.update(
        { first_name, last_name, email, mobile, gender, nationality },
        { where: { id: user_id } }
    );

    if (updatedCount === 0) {
        return {
            statusCode: 200,
            status: "failure",
            message: "Profile not found or no changes made",
            result: {},
        };
    }

    return {
        statusCode: 200,
        status: "success",
        message: "Profile updated successfully",
        result: updatedCount,
    };
};

export const changePasswordService = async (req) => {
    const { user_id } = req.user;
    const { old_password, new_password } = req.body;

    const user = await CrmCustomer.findByPk(user_id, {
        attributes: ["id", "password"],
        // raw: true // install method will not call if raw is true
    });

    if (!user) {
        return {
            statusCode: 404,
            message: "User not found",
            status: "failure",
            result: {},
        };
    }

    const isMatch = await user.comparePassword(old_password);

    if (!isMatch) {
        return {
            statusCode: 400,
            message: "Old password is incorrect",
            status: "failure",
            result: {},
        };
    }

    user.password = new_password;
    await user.save(); // beforeUpdate will hash

    return {
        statusCode: 200,
        message: "Password changed successfully",
        status: "success",
        result: {},
    };
};
