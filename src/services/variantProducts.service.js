/**
 * Normalize mixed/duplicated "alternateAttributes" into two sections:
 * 1) PRIMARY  -> one item per primary value (rep SKU/URL)
 * 2) SECONDARY-> unique values for the selected primary
 *
 * Opts:
 *  - selectedSku
 *  - hideColorsWithoutStorage (kept for compatibility) => hides primary values with no secondary mapping
 *  - primaryTitle (optional)  => force which section is primary via its Title text
 *  - rankers (optional)       => custom sort logic per section title
 */
export function pickSelectedOnlyUnique(alternateAttributes = [], opts = {}) {
    const {
        selectedSku,
        hideColorsWithoutStorage = true,
        primaryTitle, // optional override (e.g. "Available Size")
        rankers = {}, // { "available size": (name)=>rank, ... }
    } = opts;

    // --- helpers ---
    const normTitle = (t) => String(t ?? "").trim();
    const lower = (s) => String(s ?? "").toLowerCase();
    const toList = (sec) => (Array.isArray(sec?.list) ? sec.list : []);

    // Default rankers for numeric-like attributes
    const defaultRankers = {
        "available storage": (name) => {
            const v = String(name || "")
                .replace(/\s+/g, "")
                .toUpperCase(); // 512GB / 1TB
            const tb = v.match(/^(\d+(?:\.\d+)?)TB$/);
            const gb = v.match(/^(\d+(?:\.\d+)?)GB$/);
            if (tb) return parseFloat(tb[1]) * 1024;
            if (gb) return parseFloat(gb[1]);
            return Number.NEGATIVE_INFINITY;
        },
        "available size": (name) => {
            const n = parseFloat(String(name || "").trim());
            return Number.isFinite(n) ? n : Number.NEGATIVE_INFINITY;
        },
    };
    const getRanker = (title) =>
        rankers[lower(title)] ?? defaultRankers[lower(title)] ?? (() => 0);

    // Normalize sections
    const secs = (alternateAttributes || []).map((s) => ({
        id: String(s?.id ?? ""),
        title: normTitle(s?.Title ?? s?.title ?? ""),
        list: toList(s),
    }));
    if (secs.length < 2) {
        return {
            alternateAttributes: secs.map(({ title, id, list }) => ({
                title,
                id,
                list,
            })),
        };
    }

    // Decide PRIMARY and SECONDARY sections
    let primary, secondary;
    if (primaryTitle) {
        primary =
            secs.find((s) => lower(s.title) === lower(primaryTitle)) || secs[0];
        secondary = secs.find((s) => s !== primary) ||
            secs[1] || { id: "", title: "", list: [] };
    } else {
        const colorIdx = secs.findIndex((s) => /\bcolou?r\b/i.test(s.title));
        if (colorIdx >= 0) {
            primary = secs[colorIdx];
            secondary = secs.find((s, i) => i !== colorIdx) || secs[0];
        } else {
            primary = secs[0];
            secondary = secs[1];
        }
    }

    // Build lookups
    const skuToPrimary = new Map();
    for (const it of primary.list) {
        if (!it?.sku) continue;
        skuToPrimary.set(String(it.sku), {
            id: String(it.id ?? ""),
            name: String(it.name ?? ""),
            code: it.code ?? "",
            url: String(it.url ?? ""),
        });
    }
    const secRank = getRanker(secondary.title);
    const skuToSecondary = new Map();
    for (const it of secondary.list) {
        if (!it?.sku) continue;
        skuToSecondary.set(String(it.sku), {
            id: String(it.id ?? ""),
            name: String(it.name ?? ""),
            url: String(it.url ?? ""),
            _rank: Number(secRank(it.name)),
        });
    }

    // Group primary items by primary id
    const primaryIdToItems = new Map();
    for (const it of primary.list) {
        const pid = String(it?.id ?? "");
        if (!primaryIdToItems.has(pid)) primaryIdToItems.set(pid, []);
        primaryIdToItems.get(pid).push(it);
    }

    // Decide selected SKU
    let selected = selectedSku ? String(selectedSku) : null;
    if (!selected) {
        selected =
            primary.list.find((x) => Number(x?.selected) === 1 && x?.sku)
                ?.sku ||
            secondary.list.find((x) => Number(x?.selected) === 1 && x?.sku)
                ?.sku ||
            null;
        if (selected) selected = String(selected);
    }
    if (!selected) {
        let chosen = null;
        for (const [, items] of primaryIdToItems) {
            let best = null;
            for (const it of items) {
                const s2 = skuToSecondary.get(String(it.sku));
                if (!s2) continue;
                if (!best || s2._rank > best.rank)
                    best = { sku: String(it.sku), rank: s2._rank };
            }
            if (best) {
                chosen = best.sku;
                break;
            }
        }
        selected = chosen || String(primary.list[0]?.sku || "");
    }

    // Find selected primary id
    const selectedPrimaryId = String(
        primary.list.find((x) => String(x.sku) === selected)?.id ??
            primary.list[0]?.id ??
            ""
    );

    // PRIMARY output (unique per primary id)
    const primaryOutput = [];
    const seenPrimary = new Set();
    for (const [pid, items] of primaryIdToItems) {
        // Representative item:
        let rep =
            (selected && items.find((x) => String(x.sku) === selected)) ||
            (() => {
                let best = null;
                for (const it of items) {
                    const s2 = skuToSecondary.get(String(it.sku));
                    if (!s2) continue;
                    if (!best || s2._rank > best._rank)
                        best = { ...it, _rank: s2._rank };
                }
                return best || items[0];
            })();

        if (hideColorsWithoutStorage) {
            const hasAnySecondary = items.some((x) =>
                skuToSecondary.has(String(x.sku))
            );
            if (!hasAnySecondary) continue;
        }

        if (!seenPrimary.has(pid)) {
            seenPrimary.add(pid);
            primaryOutput.push({
                code: rep.code ?? "",
                name: rep.name ?? "",
                id: String(rep.id ?? ""),
                sku: String(rep.sku ?? ""),
                url: String(rep.url ?? ""),
                selected: pid === selectedPrimaryId ? 1 : 0,
            });
        }
    }
    for (const p of primaryOutput)
        p.selected = p.id === selectedPrimaryId ? 1 : 0;

    // SECONDARY output (unique values for the selected primary)
    const secondMap = new Map();
    const selectedPrimaryItems = primaryIdToItems.get(selectedPrimaryId) || [];
    for (const it of selectedPrimaryItems) {
        const s2 = skuToSecondary.get(String(it.sku));
        if (!s2) continue;
        const key = `${s2.id}|${lower(s2.name)}`;
        if (!secondMap.has(key)) {
            secondMap.set(key, {
                code: "",
                name: s2.name,
                id: s2.id,
                sku: String(it.sku),
                url: s2.url || String(it.url ?? ""),
                selected: selected && String(it.sku) === selected ? 1 : 0,
                _rank: s2._rank,
            });
        } else {
            const cur = secondMap.get(key);
            if (selected && String(it.sku) === selected) {
                cur.sku = String(it.sku);
                cur.url = s2.url || String(it.url ?? "");
                cur.selected = 1;
            }
        }
    }
    const secondaryOutput = Array.from(secondMap.values())
        .sort((a, b) => b._rank - a._rank)
        .map(({ _rank, ...rest }) => rest);

    if (
        !secondaryOutput.some((x) => x.selected === 1) &&
        secondaryOutput.length
    ) {
        secondaryOutput[0].selected = 1;
    }

    // Return with ORIGINAL (dynamic) titles/ids
    return {
        alternateAttributes: [
            { title: primary.title, id: primary.id, list: primaryOutput },
            { title: secondary.title, id: secondary.id, list: secondaryOutput },
        ],
    };
}
