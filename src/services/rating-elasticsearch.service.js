import CrmRatings from "../models/crm_ratings.model.js";
import { Op } from "sequelize";

/**
 * Calculate average rating and count for a product
 * @param {number} productId - Product ID
 * @returns {Promise<{avgRating: number, ratingCount: number}>}
 */
export const calculateProductRatingStats = async (productId) => {
    try {
        const stats = await CrmRatings.findOne({
            attributes: [
                [
                    CrmRatings.sequelize.fn(
                        "AVG",
                        CrmRatings.sequelize.col("rating")
                    ),
                    "avgRating",
                ],
                [
                    CrmRatings.sequelize.fn(
                        "COUNT",
                        CrmRatings.sequelize.col("rating")
                    ),
                    "ratingCount",
                ],
            ],
            where: {
                productid: productId,
                rstatus: 1, // Only active ratings
                rating: {
                    [Op.ne]: null, // Exclude null ratings
                },
            },
            raw: true,
        });

        return {
            avgRating: parseFloat(stats?.avgRating || 0),
            ratingCount: parseInt(stats?.ratingCount || 0),
        };
    } catch (error) {
        console.error("Error calculating product rating stats:", error);
        return {
            avgRating: 0,
            ratingCount: 0,
        };
    }
};

/**
 * Update product rating data in Elasticsearch using Painless script
 * @param {Object} fastify - Fastify instance with elasticsearch client
 * @param {number} productId - Product ID
 * @param {number} avgRating - Average rating
 * @param {number} ratingCount - Total rating count
 */
export const updateProductRatingInElasticsearch = async (
    fastify,
    productId,
    avgRating,
    ratingCount
) => {
    try {
        const client = fastify.elasticsearch;
        const indexName = process.env.ELASTIC_PRODUCT_INDEX;

        if (!client || !indexName) {
            console.warn("Elasticsearch client or index not configured");
            return;
        }

        // Update using Painless script for better control and atomic operations
        const updateBody = {
            script: {
                source: `
                    ctx._source.avg_rating = params.avg_rating;
                    ctx._source.rating_count = params.rating_count;
                    ctx._source.updated_date = params.updated_date;
                    if (ctx._source.ratings == null) {
                        ctx._source.ratings = [:];
                    }
                    ctx._source.ratings.avg_rating = params.avg_rating;
                    ctx._source.ratings.count = params.rating_count;
                    ctx._source.ratings.last_updated = params.updated_date;
                `,
                lang: "painless",
                params: {
                    avg_rating: parseFloat(avgRating.toFixed(2)),
                    rating_count: parseInt(ratingCount),
                    updated_date: new Date().toISOString(),
                },
            },
            upsert: {
                avg_rating: parseFloat(avgRating.toFixed(2)),
                rating_count: parseInt(ratingCount),
                ratings: {
                    avg_rating: parseFloat(avgRating.toFixed(2)),
                    count: parseInt(ratingCount),
                    last_updated: new Date().toISOString(),
                },
                updated_date: new Date().toISOString(),
            },
        };

        await client.update({
            index: indexName,
            id: productId.toString(),
            body: updateBody,
            retry_on_conflict: 3,
        });

        console.log(
            `Updated Elasticsearch rating for product ${productId}: avg=${avgRating}, count=${ratingCount}`
        );
    } catch (error) {
        if (error.meta?.statusCode === 404) {
            console.warn(
                `Product ${productId} not found in Elasticsearch index`
            );
        } else {
            console.error(
                "Error updating product rating in Elasticsearch:",
                error
            );
        }
    }
};

/**
 * Handle rating insertion/update - calculate stats and update Elasticsearch
 * @param {Object} fastify - Fastify instance
 * @param {number} productId - Product ID
 */
export const handleRatingUpdate = async (fastify, productId) => {
    try {
        if (!productId) {
            console.warn("Product ID is required for rating update");
            return;
        }

        // Calculate new rating statistics
        const stats = await calculateProductRatingStats(productId);

        // Update Elasticsearch with new stats
        await updateProductRatingInElasticsearch(
            fastify,
            productId,
            stats.avgRating,
            stats.ratingCount
        );

        return stats;
    } catch (error) {
        console.error("Error handling rating update:", error);
        throw error;
    }
};

/**
 * Batch update multiple products' ratings in Elasticsearch
 * @param {Object} fastify - Fastify instance
 * @param {Array<number>} productIds - Array of product IDs
 */
export const batchUpdateProductRatings = async (fastify, productIds) => {
    try {
        const client = fastify.elasticsearch;
        const indexName = process.env.ELASTIC_PRODUCT_INDEX;

        if (
            !client ||
            !indexName ||
            !Array.isArray(productIds) ||
            productIds.length === 0
        ) {
            return;
        }

        const bulkOperations = [];

        // Calculate stats for each product
        for (const productId of productIds) {
            const stats = await calculateProductRatingStats(productId);

            // Add update operation to bulk array
            bulkOperations.push({
                update: {
                    _index: indexName,
                    _id: productId.toString(),
                },
            });

            bulkOperations.push({
                script: {
                    source: `
                        ctx._source.avg_rating = params.avg_rating;
                        ctx._source.rating_count = params.rating_count;
                        ctx._source.updated_date = params.updated_date;
                        if (ctx._source.ratings == null) {
                            ctx._source.ratings = [:];
                        }
                        ctx._source.ratings.avg_rating = params.avg_rating;
                        ctx._source.ratings.count = params.rating_count;
                        ctx._source.ratings.last_updated = params.updated_date;
                    `,
                    lang: "painless",
                    params: {
                        avg_rating: parseFloat(stats.avgRating.toFixed(2)),
                        rating_count: parseInt(stats.ratingCount),
                        updated_date: new Date().toISOString(),
                    },
                },
                upsert: {
                    avg_rating: parseFloat(stats.avgRating.toFixed(2)),
                    rating_count: parseInt(stats.ratingCount),
                    ratings: {
                        avg_rating: parseFloat(stats.avgRating.toFixed(2)),
                        count: parseInt(stats.ratingCount),
                        last_updated: new Date().toISOString(),
                    },
                    updated_date: new Date().toISOString(),
                },
            });
        }

        // Execute bulk update
        if (bulkOperations.length > 0) {
            const response = await client.bulk({
                body: bulkOperations,
                refresh: "wait_for",
            });

            if (response.errors) {
                console.error("Some bulk operations failed:", response.items);
            } else {
                console.log(
                    `Bulk updated ${productIds.length} products' ratings in Elasticsearch`
                );
            }
        }
    } catch (error) {
        console.error("Error in batch update of product ratings:", error);
    }
};

/**
 * Example function showing how to update product ratings using direct Elasticsearch API
 * Similar to your inventory update example
 * @param {Object} client - Elasticsearch client
 * @param {string} indexName - Index name
 * @param {number} productId - Product ID
 * @param {number} avgRating - Average rating
 * @param {number} ratingCount - Rating count
 */
export const updateProductRatingDirect = async (
    client,
    indexName,
    productId,
    avgRating,
    ratingCount
) => {
    try {
        const response = await client.update({
            index: indexName,
            id: productId.toString(),
            body: {
                script: {
                    source: `
                        ctx._source.avg_rating = params.avg_rating;
                        ctx._source.rating_count = params.rating_count;
                        ctx._source.updated_date = params.updated_date;
                        if (ctx._source.ratings == null) {
                            ctx._source.ratings = [:];
                        }
                        ctx._source.ratings.avg_rating = params.avg_rating;
                        ctx._source.ratings.count = params.rating_count;
                        ctx._source.ratings.last_updated = params.updated_date;
                    `,
                    lang: "painless",
                    params: {
                        avg_rating: parseFloat(avgRating.toFixed(2)),
                        rating_count: parseInt(ratingCount),
                        updated_date: new Date().toISOString(),
                    },
                },
            },
        });

        console.log(
            `Direct update successful for product ${productId}:`,
            response
        );
        return response;
    } catch (error) {
        console.error(`Direct update failed for product ${productId}:`, error);
        throw error;
    }
};
