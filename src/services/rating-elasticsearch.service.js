import CrmRatings from "../models/crm_ratings.model.js";
import { Op } from "sequelize";

/**
 * Calculate average rating and count for a product
 * @param {number} productId - Product ID
 * @returns {Promise<{avgRating: number, ratingCount: number}>}
 */
export const calculateProductRatingStats = async (productId) => {
    try {
        const stats = await CrmRatings.findOne({
            attributes: [
                [CrmRatings.sequelize.fn('AVG', CrmRatings.sequelize.col('rating')), 'avgRating'],
                [CrmRatings.sequelize.fn('COUNT', CrmRatings.sequelize.col('rating')), 'ratingCount']
            ],
            where: {
                productid: productId,
                rstatus: 1, // Only active ratings
                rating: {
                    [Op.ne]: null // Exclude null ratings
                }
            },
            raw: true
        });

        return {
            avgRating: parseFloat(stats?.avgRating || 0),
            ratingCount: parseInt(stats?.ratingCount || 0)
        };
    } catch (error) {
        console.error('Error calculating product rating stats:', error);
        return {
            avgRating: 0,
            ratingCount: 0
        };
    }
};

/**
 * Update product rating data in Elasticsearch
 * @param {Object} fastify - Fastify instance with elasticsearch client
 * @param {number} productId - Product ID
 * @param {number} avgRating - Average rating
 * @param {number} ratingCount - Total rating count
 */
export const updateProductRatingInElasticsearch = async (fastify, productId, avgRating, ratingCount) => {
    try {
        const client = fastify.elasticsearch;
        const indexName = process.env.ELASTIC_PRODUCT_INDEX;

        if (!client || !indexName) {
            console.warn('Elasticsearch client or index not configured');
            return;
        }

        // Update the product document with rating information
        const updateBody = {
            doc: {
                avg_rating: avgRating,
                rating_count: ratingCount,
                updated_date: new Date().toISOString()
            },
            doc_as_upsert: false // Don't create if document doesn't exist
        };

        await client.update({
            index: indexName,
            id: productId.toString(),
            body: updateBody,
            retry_on_conflict: 3
        });

        console.log(`Updated Elasticsearch rating for product ${productId}: avg=${avgRating}, count=${ratingCount}`);
    } catch (error) {
        if (error.meta?.statusCode === 404) {
            console.warn(`Product ${productId} not found in Elasticsearch index`);
        } else {
            console.error('Error updating product rating in Elasticsearch:', error);
        }
    }
};

/**
 * Handle rating insertion/update - calculate stats and update Elasticsearch
 * @param {Object} fastify - Fastify instance
 * @param {number} productId - Product ID
 */
export const handleRatingUpdate = async (fastify, productId) => {
    try {
        if (!productId) {
            console.warn('Product ID is required for rating update');
            return;
        }

        // Calculate new rating statistics
        const stats = await calculateProductRatingStats(productId);
        
        // Update Elasticsearch with new stats
        await updateProductRatingInElasticsearch(
            fastify, 
            productId, 
            stats.avgRating, 
            stats.ratingCount
        );

        return stats;
    } catch (error) {
        console.error('Error handling rating update:', error);
        throw error;
    }
};

/**
 * Batch update multiple products' ratings in Elasticsearch
 * @param {Object} fastify - Fastify instance
 * @param {Array<number>} productIds - Array of product IDs
 */
export const batchUpdateProductRatings = async (fastify, productIds) => {
    try {
        const client = fastify.elasticsearch;
        const indexName = process.env.ELASTIC_PRODUCT_INDEX;

        if (!client || !indexName || !Array.isArray(productIds) || productIds.length === 0) {
            return;
        }

        const bulkOperations = [];

        // Calculate stats for each product
        for (const productId of productIds) {
            const stats = await calculateProductRatingStats(productId);
            
            // Add update operation to bulk array
            bulkOperations.push({
                update: {
                    _index: indexName,
                    _id: productId.toString()
                }
            });
            
            bulkOperations.push({
                doc: {
                    avg_rating: stats.avgRating,
                    rating_count: stats.ratingCount,
                    updated_date: new Date().toISOString()
                },
                doc_as_upsert: false
            });
        }

        // Execute bulk update
        if (bulkOperations.length > 0) {
            const response = await client.bulk({
                body: bulkOperations,
                refresh: 'wait_for'
            });

            if (response.errors) {
                console.error('Some bulk operations failed:', response.items);
            } else {
                console.log(`Bulk updated ${productIds.length} products' ratings in Elasticsearch`);
            }
        }
    } catch (error) {
        console.error('Error in batch update of product ratings:', error);
    }
};
