import { currentDateTime } from "../utils/dateTimeHandler.js";
import jwt from "jsonwebtoken";
import {
    sendSMS,
    sendSMSByDoveSoft,
    sendSMSWhatsApp,
} from "./common.service.js";

import CrmCustomer from "../models/crm_customer.model.js";
import OmsCartTracking from "../models/oms_cart_tracking.model.js";
import TmpOtpVerification from "../models/otp_verification.model.js";
import OurshopeeNewsletter from "../models/ourshopee_newsletter.model.js";
import { sendEmailOtpService } from "./vendor.service.js";

export const checkEmailExistence = async (email) => {
    try {
        const result = await CrmCustomer.findOne({
            attributes: ["email", "first_name"],
            where: { email },
            raw: true,
        });

        return result || null;
    } catch (err) {
        throw err;
    }
};

export const checkMobileOTP = async (req, type = "mobile", isMail = false) => {
    try {
        const mobile = req.body.mobile.replace(/\s/g, "");
        const countryId = req.country_id;
        const record = await TmpOtpVerification.findOne({
            where: { email_number: mobile },
            raw: true,
        });
        let otp;
        const currentDate = currentDateTime();
        if (isMail) {
            otp = Math.floor(100000 + Math.random() * 900000);
        } else {
            otp = Math.floor(1000 + Math.random() * 9000);
        }

        if (record) {
            const createdAt = new Date(record.created_date);

            const sendtime = record.sendtime;

            // Load configuration from environment
            const OTP_BLOCK_MINUTES = parseInt(
                process.env.OTP_BLOCK_MINUTES || "10",
                10
            );
            const OTP_BLOCK_LIMIT = parseInt(
                process.env.OTP_BLOCK_LIMIT || "5",
                10
            );

            // Convert millisecond diff into actual minutes
            const diffInMs = new Date(currentDate) - createdAt;
            const diffInMinutes = diffInMs / (1000 * 60); // convert to minutes

            if (
                diffInMinutes <= OTP_BLOCK_MINUTES &&
                sendtime >= OTP_BLOCK_LIMIT
            ) {
                const remainingMinutes = Math.ceil(
                    OTP_BLOCK_MINUTES - diffInMinutes
                );
                const message = formatBlockMessage(remainingMinutes);

                return {
                    success: "blocked",
                    message,
                    data: [],
                    statusCode: 200,
                };
            }
        }

        if (record) {
            let sendtime = record.sendtime + 1;
            if (sendtime > 3) sendtime = 1;

            await TmpOtpVerification.update(
                { otp: otp.toString(), created_date: currentDate, sendtime },
                { where: { id: record.id } }
            );
        } else {
            await TmpOtpVerification.create({
                email_number: mobile,
                otp: otp.toString(),
                created_date: currentDate,
                sendtime: 1,
            });
        }

        const message = `Hi, Use ${otp} to verify your phone number.`;

        if (type == "mobile") {
            const cid = Number(countryId);
            if ([2, 3].includes(cid)) {
                // console.log('if in')
                sendSMSByDoveSoft(countryId, mobile, message);
            } else {
                // console.log('else in')
                sendSMS(countryId, mobile, message);
            }
        }
        let successMessage;
        successMessage = `OTP sent to ${mobile}. Please check and verify.`;
        if (type == "whatsup") sendSMSWhatsApp(countryId, mobile, message);

        if (isMail === true) {
            let info = {
                email: req.body.email,
                message: `Hi, Use ${otp} to verify your email/phone number.`,
            };

            successMessage = `OTP sent to email/phone. Please check and verify.`;

            sendEmailOtpService(info);
        }

        return {
            status: "success",
            message: successMessage,
            data: [],
            statusCode: 200,
        };
    } catch (err) {
        throw err;
    }
};

function formatBlockMessage(blockMinutes) {
    const hours = Math.floor(blockMinutes / 60);
    const minutes = blockMinutes % 60;
    const parts = [];

    if (hours > 0) {
        parts.push(`${hours} hour${hours > 1 ? "s" : ""}`);
    }

    if (minutes > 0) {
        parts.push(`${minutes} minute${minutes > 1 ? "s" : ""}`);
    }

    const formattedDuration = parts.join(" and ");

    return `Maximum OTP limit reached. Try again in ${formattedDuration}.`;
}

export const insertOTP = async (mobile) => {
    try {
        const currentTime = currentDateTime();
        const otp = Math.floor(1000 + Math.random() * 9000);
        const record = await TmpOtpVerification.create({
            email_number: mobile,
            otp,
            created_date: currentTime,
        });
        return { success: !!record, otp };
    } catch (err) {
        throw err;
    }
};

export const verifyOtpRecord = async (mobile, otp) => {
    try {
        const result = await TmpOtpVerification.findOne({
            where: { email_number: mobile, otp },
            attributes: ["id"],
            raw: true,
        });
        console.log("result", result);
        return result || null;
    } catch (err) {
        throw err;
    }
};

export const deleteOtpById = async (id) => {
    try {
        await TmpOtpVerification.destroy({ where: { id } });
        return true;
    } catch (err) {
        throw err;
    }
};

export const registerUser = async (payload) => {
    const {
        first_name,
        last_name = "",
        gender,
        nationality,
        email,
        password,
        mobile,
        country = "",
        offersale,
    } = payload;

    try {
        const existing = await checkEmailExistence(email);

        if (existing) {
            return {
                message:
                    "This email is already registered with us...Please login to continue",
                success: "error",
                data: [],
                statusCode: 200,
            };
        }

        // const hashedPassword = await hashPassword(password);
        const otp = Math.floor(1000 + Math.random() * 9000);
        const date = currentDateTime();

        const user = await CrmCustomer.create({
            first_name,
            last_name,
            gender,
            nationality,
            email,
            password,
            mobile,
            date,
            refid: otp,
            country_id: country,
        });

        if (offersale == 1) {
            await OurshopeeNewsletter.create({
                email,
                status: 1,
                email_status: 1,
            });
        }

        const token = jwt.sign(
            { user_id: user.id, first_name, last_name, email },
            process.env.TOKEN_SECRET,
            { expiresIn: process.env.TOKEN_EXP }
        );
        return {
            message: "User register successfully",
            success: "success",
            data: { token },
            statusCode: 200,
        };
    } catch (err) {
        throw err;
    }
};

export const authenticateUser = async (email, password, oscad) => {
    try {
        const user = await CrmCustomer.findOne({
            attributes: [
                "id",
                "first_name",
                "last_name",
                "email",
                "mobile",
                "password",
            ],
            where: { email },
        });

        if (!user) {
            return {
                message: "Invalid credentials",
                success: "failure",
                data: {},
                statusCode: 200,
            };
        }

        const isValid = await user.comparePassword(password);

        if (!isValid) {
            return {
                message: "Please enter valid password",
                success: "failure",
                data: {},
                statusCode: 200,
            };
        }

        const token = jwt.sign(
            {
                user_id: user.id,
                first_name: user.first_name,
                mobile: user.mobile,
                email: user.email,
            },
            process.env.TOKEN_SECRET,
            { expiresIn: process.env.TOKEN_EXP }
        );

        // Update cart tracking by IP (oscad)
        if (oscad && parseInt(oscad) > 0) {
            await OmsCartTracking.update(
                { customer_id: user.id },
                { where: { ip_address: oscad } }
            );
        }

        return {
            message: "Login successful",
            success: "success",
            data: { success: true, token },
            statusCode: 200,
        };
    } catch (err) {
        throw err;
    }
};

export const verifyResetToken = async (token) => {
    try {
        const decoded = jwt.verify(token, process.env.TOKEN_SECRET);

        if (!decoded?.email) {
            return {
                message: "Please provide valid token",
                success: "error",
                data: {},
                statusCode: 200,
            };
        }

        await CrmCustomer.findOne({
            attributes: ["email"],
            where: { email: decoded.email },
            raw: true,
        });

        return {
            message: "Token verified successfully",
            success: "success",
            data: { email: decoded.email },
            statusCode: 200,
        };
    } catch (err) {
        throw err;
    }
};

export const updatePassword = async (email, password) => {
    try {
        await CrmCustomer.save({ password }, { where: { email } });
        return true;
    } catch (err) {
        throw err;
    }
};
