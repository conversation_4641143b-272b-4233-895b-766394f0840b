import { currentDate, currentDateTime } from "../utils/dateTimeHandler.js";
import moment from "moment";
import { Op } from "sequelize";

import OmsCartTracking from "../models/oms_cart_tracking.model.js";
import CatalogPromotion from "../models/catalog_promotion.model.js";
import CatalogCategory from "../models/catalog_category.model.js";
import AdminCountryArea from "../models/admin_country_area.model.js";
import AdminCountryEmirates from "../models/admin_country_emirates.model.js";
import CrmCustomerAddress from "../models/crm_customer_address.model.js";
import OmsPaymentMethod from "../models/oms_payment_method.model.js";
import OmsOrders from "../models/oms_orders.model.js";

import { processOrder } from "./payment.service.js";
import { getMyProfileService } from "./profile.service.js";
import { DeliveryExpectedBy } from "./product.service.js";

import {
    CURRENCY,
    VAT,
    FREE_SHIPPING_SUBTOTAL_VALUE,
    PROCESSING_FEE_COD,
    SHOW_COD_FINAL_TOTAL_VALUE,
    FREE_SHIPPING_SUBCATEGORIES,
    countryPhonePrefixes,
    ORDER_FROM_ID,
    COUNTRY_NAMES,
} from "../region/config.js";

import {
    getProductsByIdsElk,
    getProductsBySectionElk,
} from "../elk/common.elk.js";

export const AddToCart = async (request) => {
    try {
        const { country_id } = request || request.body.country_id;
        let ip_address =
            request.body.ip_address && request.body.ip_address > 0
                ? request.body.ip_address
                : Math.floor(Math.random() * 1000000000);

        const currentDate = currentDateTime();
        const user_id = request.body.user_id;
        const product_id = request.body.product_id;
        const vendor_id = request.body.vendor_id;
        const body = request.body;

        // Step 1: Check if product already in cart
        const existingCart = await OmsCartTracking.findOne({
            attributes: [
                "track_id",
                "product_id",
                "total_order",
                "ip_address",
                "vendor_id",
            ],
            where: {
                product_id,
                rstatus: 1,
                country_id,
                vendor_id,
                ...(user_id > 0 ? { customer_id: user_id } : { ip_address }),
            },
        });

        if (existingCart) {
            const existing = existingCart;
            const total_order =
                parseInt(existing.total_order) + parseInt(body.quantity || 1);
            const track_id = existing.track_id;
            const productIds = [{ productId: product_id, vendorId: vendor_id }];

            const [product] = await getProductsByIdsElk(request, productIds);

            if (product) {
                const now = moment(currentDate).unix();
                const from =
                    product.from_date !== "0000-00-00 00:00:00"
                        ? moment(product.from_date).unix()
                        : 0;
                const to =
                    product.to_date !== "0000-00-00 00:00:00"
                        ? moment(product.to_date).unix()
                        : 0;
                const isDeal = now > from && now < to;

                if (!isDeal) {
                    const cart = await OmsCartTracking.update(
                        { total_order: total_order },
                        {
                            where: {
                                track_id: track_id,
                                country_id: country_id,
                                vendor_id,
                            },
                        }
                    );

                    if (cart) {
                        return { cart_id: track_id, ip_address };
                    }
                } else {
                    return {
                        cart_id: track_id,
                        ip_address,
                        msg: "Only one deal product is eligible per order.",
                    };
                }
            } else {
                return { cart_id: 0, ip_address, msg: "Product not found." };
            }
        } else {
            // Item not in cart - check existing items
            const where = {
                country_id,
                rstatus: 1,
                vendor_id,
                ...(user_id > 0
                    ? {
                          customer_id: user_id,
                          ip_address,
                      }
                    : {
                          ip_address,
                      }),
            };

            const cartList = await OmsCartTracking.findAll({
                attributes: [
                    ["track_id", "cart_id"],
                    "product_id",
                    "total_order",
                    "add_date",
                    "country_id",
                    "vendor_id",
                ],
                where,
            });

            let dealofthedayf = 0;
            let dealofthedayb = 0;
            let unmatched = [];

            if (cartList.length > 0) {
                const productIds = [];
                cartList.forEach((item) =>
                    productIds.push({
                        productId: item.product_id,
                        vendor_id: item.vendor_id,
                    })
                );
                const productListDB = await getProductsByIdsElk(
                    request,
                    productIds
                );
                const productMap = new Map(productListDB.map((p) => [p.id, p]));

                for (const cart of cartList) {
                    const product = productMap.get(cart.product_id);
                    if (product) {
                        const now = moment(currentDate).unix();
                        const from =
                            product.from_date !== "0000-00-00 00:00:00"
                                ? moment(product.from_date).unix()
                                : 0;
                        const to =
                            product.to_date !== "0000-00-00 00:00:00"
                                ? moment(product.to_date).unix()
                                : 0;
                        if (now > from && now < to) {
                            dealofthedayb = 1;
                        }
                    } else {
                        unmatched.push(cart.cart_id);
                    }
                }

                if (unmatched.length > 0) {
                    await OmsCartTracking.destroy({
                        where: {
                            track_id: unmatched,
                            country_id,
                            ip_address,
                            vendor_id,
                            ...(user_id > 0 ? { customer_id: user_id } : {}),
                        },
                    });
                }

                // Check if new product is a deal and one already exists
                const [productFront] = await getProductsByIdsElk(request, [
                    { productId: product_id, vendorId: vendor_id },
                ]);
                if (productFront) {
                    const now = moment(currentDate).unix();
                    const from =
                        productFront.from_date !== "0000-00-00 00:00:00"
                            ? moment(productFront.from_date).unix()
                            : 0;
                    const to =
                        productFront.to_date !== "0000-00-00 00:00:00"
                            ? moment(productFront.to_date).unix()
                            : 0;
                    if (now > from && now < to && dealofthedayb === 1) {
                        dealofthedayf = 1;
                    }
                }
            }

            if (dealofthedayf === 0) {
                body.action = "add";
                body.add_date = currentDate;

                const insertRes = await OmsCartTracking.create({
                    customer_id: user_id,
                    product_id,
                    ip_address,
                    country_id,
                    action: body.action,
                    add_date: body.add_date,
                    total_order: parseInt(body.quantity) || 1,
                    vendor_id,
                });
                if (insertRes.dataValues.track_id != null) {
                    return {
                        cart_id: insertRes.dataValues.track_id,
                        ip_address,
                    };
                }
            } else {
                return {
                    cart_id: 0,
                    ip_address,
                    msg: "Only one deal product is eligible per order.",
                };
            }
        }
    } catch (err) {
        console.log("error from AddToCart service", err);
        throw err;
    }
};

export const GetFromCart = async (request) => {
    try {
        const { country_id } = request || request.body;
        const currency = CURRENCY[country_id].currency;
        const currentDate = currentDateTime();
        let ip_address = request.body.ip_address || 0;
        let user_id = request.body.user_id || 0;

        if (ip_address && ip_address > 0) {
            if (user_id > 0) {
                await OmsCartTracking.update(
                    { customer_id: user_id },
                    {
                        where: {
                            ip_address,
                            country_id,
                        },
                    }
                );
            }
        } else if (!request.body.user_id || request.body.user_id === 0) {
            // return "notfound";
        }

        const where =
            user_id > 0
                ? {
                      customer_id: user_id,
                      country_id,
                      rstatus: 1,
                  }
                : {
                      ip_address,
                      country_id,
                      rstatus: 1,
                  };

        const cartItems = await OmsCartTracking.findAll({
            attributes: [
                ["track_id", "cart_id"],
                "product_id",
                "total_order",
                "add_date",
                "country_id",
                "vendor_id",
            ],
            where,
        });
        if (cartItems.length === 0) {
            return {
                result: [],
                grand_total: currency + " 0.00",
                msg: "",
            };
        }

        const productIds = [];
        cartItems.forEach((order) => {
            productIds.push({
                productId: order.product_id,
                vendorId: order?.dataValues?.vendor_id,
            });
        });
        // not getting used in the code anywhere
        // remove it if not needed in the future
        // await CatalogPromotion.findOne();
        const productList = await getProductsByIdsElk(request, productIds);

        const combinedCart = cartItems.map((cart) =>
            Object.assign(
                cart.dataValues,
                productList.find(
                    (product) =>
                        product.id === cart.dataValues?.product_id &&
                        product.vendor_id === cart.dataValues?.vendor_id
                )
            )
        );

        let grand_total = 0;
        let free_shipping = 0;
        let deal_product_id = 0;
        let minpur = 0;

        const output = await Promise.all(
            combinedCart.map(async (Element) => {
                // Khusboo

                const deliveryData =
                    (await DeliveryExpectedBy?.(Element.sku, country_id)) || {};

                let available_quantity = Element.quantity || 0;
                let outofstock = available_quantity <= 0 ? 1 : 0;

                const today = moment(currentDate).unix();
                const promotion_from = moment(Element.from_date).unix();
                const promotion_to = moment(Element.to_date).unix();

                let price = 0;
                let min_tempvalue = 0;

                if (
                    parseInt(Element.promotion_price) > 0 &&
                    today > promotion_from &&
                    today < promotion_to
                ) {
                    price = Element.promotion_price;
                    deal_product_id = 1;
                    min_tempvalue = Element.min_pur || 0;
                } else if (parseInt(Element.special_price) > 0) {
                    price = Element.special_price;
                } else price = Element.price;
                // skip invalid prices
                if (!price || isNaN(price) || price <= 0) return null;

                if (Element.shipping_charge === 0) {
                    free_shipping++;
                    min_tempvalue = Element.min_pur || min_tempvalue;
                }

                minpur =
                    free_shipping > 0 ? min_tempvalue || 50 : min_tempvalue;

                const percentage =
                    ((Element.price - price) / Element.price) * 100;
                const total = (Element.total_order * price).toFixed(2);
                grand_total += parseFloat(total);
                const data = {
                    cart_id: Element.cart_id,
                    name: Element.name,
                    url: Element.url,
                    product_id: Element.product_id,
                    quantity: Element.total_order,
                    single_price: price,
                    sku: Element.sku,
                    available_quantity,
                    outofstock,
                    total,
                    image: Element.image?.replace("/thump", "") || "",
                    percentage: Math.round(percentage),
                    old_price: Element.price,
                    fastTrack: deliveryData.fastTrack,
                    delivery: deliveryData.delivery,
                    subcategory_id: Element.subcategory_id,
                    stock: Element.stock,
                    brand: Element.brand_name,
                };
                return data;
            })
        );
        const filteredOutput = output.filter(Boolean);

        let msg = "";
        if (grand_total < minpur && deal_product_id == 0) {
            msg = `Minimum purchase amount should be ${currency} ${minpur} & above`;
        } else if (grand_total < minpur && deal_product_id == 1) {
            msg = `Only one deal product is eligible per order and minimum purchase amount should be ${currency} ${minpur}`;
        }

        return {
            result: filteredOutput,
            grand_total: `${currency} ${addZeroes(grand_total)}`,
            msg,
        };
    } catch (err) {
        console.log("error from GetFromCart service", err);
        throw err;
    }
};

export const changeCartQuantity = async (request) => {
    try {
        // check if cart_id and quantity are not undefined are not null
        if (request.body?.cart_id && request.body?.quantity) {
            const currentDate = await currentDateTime();
            const cartData = await OmsCartTracking.findOne({
                attributes: [
                    "track_id",
                    "product_id",
                    "total_order",
                    "ip_address",
                    "vendor_id",
                ],
                where: { track_id: request.body.cart_id },
            });

            if (cartData.product_id) {
                const productListFront = await getProductsByIdsElk(request, [
                    {
                        productId: cartData.product_id,
                        vendorId: cartData.vendor_id,
                    },
                ]);
                const dealOfTheDay = 0;
                await Promise.all(
                    productListFront.map(async (product) => {
                        const now = moment(currentDate).unix();
                        const from =
                            product.from_date !== "0000-00-00 00:00:00"
                                ? moment(product.from_date).unix()
                                : 0;
                        const to =
                            product.to_date !== "0000-00-00 00:00:00"
                                ? moment(product.to_date).unix()
                                : 0;
                        if (now > from && now < to) {
                            dealOfTheDay = 1;
                        }
                    })
                );

                if (dealOfTheDay === 0) {
                    const cart = await OmsCartTracking.update(
                        { total_order: request.body.quantity },
                        { where: { track_id: request.body.cart_id } }
                    );

                    if (cart) {
                        return { cart_id: request.body.cart_id };
                    } else {
                        return "error";
                    }
                } else {
                    return {
                        cart_id: request.body.cart_id,
                        msg: "Only one deal product is eligible per order.",
                    };
                }
            }
        } else {
            return "error";
        }
    } catch (err) {
        console.log("error from changeCartQuantity service", err);
        throw err;
    }
};

export const removeFromCart = async (request) => {
    try {
        const { country_id } = request || request.body.country_id;

        if (request.body?.cart_id) {
            const removedItem = await OmsCartTracking.update(
                { rstatus: 0 },
                {
                    where: {
                        track_id: request.body.cart_id,
                        country_id,
                    },
                }
            );

            if (removedItem.length > 0) {
                return true;
            } else {
                return "error";
            }
        } else {
            return "error";
        }
    } catch (err) {
        console.log("error from changeCartQuantity service", err);
        throw err;
    }
};

export const updateCartStatus = async (request) => {
    try {
        const { country_id } = request || request.body.country_id;

        if (request.user?.user_id) {
            const updatedCart = await OmsCartTracking.update(
                { rstatus: 2 },
                {
                    where: {
                        customer_id: request.user.user_id,
                        rstatus: 1,
                        country_id,
                    },
                }
            );

            if (updatedCart.length > 0) {
                return true;
            } else {
                return "error";
            }
        } else {
            return "error";
        }
    } catch (err) {
        console.log("error from updateCartStatus service", err);
        throw err;
    }
};

const getSubCategoryById = async (subCatId = "") => {
    try {
        if (subCatId) {
            const subCat = await CatalogCategory.findOne({
                attributes: ["tabby_type", "cashew_type", "tamara_type"],
                where: { categoryid: subCatId },
            });

            if (subCat) {
                return subCat;
            } else {
                return "error";
            }
        } else {
            return "error";
        }
    } catch (err) {
        console.log("error from getSubCategoryById service", err);
        throw err;
    }
};

const getProcessingFees = async (paymentType) => {
    try {
        const processingFee = await OmsPaymentMethod.findOne({
            attributes: ["processing_fee"],
            where: {
                status: 1,
                payment_type: paymentType,
            },
        });

        return processingFee
            ? processingFee?.dataValues?.processing_fee
            : "error";
    } catch (err) {
        console.log("error from getProcessingFees service", err);
        throw err;
    }
};

const getShippingChargeFromArea = async (area_id) => {
    try {
        const area = await AdminCountryArea.findOne({
            where: { area_id },
            attributes: ["shipping_charge"],
        });

        if (area) {
            return area.dataValues.shipping_charge;
        } else {
            return false;
        }
    } catch (err) {
        console.log("error from getShippingChargeFromArea service", err);
        throw err;
    }
};

export const getUserDefaultAddress = async (loginId) => {
    try {
        const defaultAddress = await CrmCustomerAddress.findOne({
            where: {
                customer_id: loginId,
                default_address: 1,
            },
        });
        if (defaultAddress) return defaultAddress?.dataValues;

        const fallbackAddress = await CrmCustomerAddress.findOne({
            where: {
                customer_id: loginId,
                default_address: 0,
            },
        });
        return fallbackAddress?.dataValues;
    } catch (err) {
        console.log("error from getUserDefaultAddress service", err);
        throw err;
    }
};

const updateCartTrackingMuliple = async (loginId, update_cart) => {
    try {
        const cart = await OmsCartTracking.update(
            { action: "checkout" },
            { where: { track_id: update_cart } }
        );
        return cart ? true : "error";
    } catch (err) {
        console.log("error from updateCartTrackingMuliple service", err);
        throw err;
    }
};

export const GetPlaceOrder = async (request) => {
    try {
        const { country_id } = request || request.body.country_id;
        const cartData = await OmsCartTracking.findAll({
            attributes: [
                ["track_id", "cart_id"],
                "product_id",
                "total_order",
                "add_date",
                "country_id",
                "vendor_id",
                ["action", "traking_action"],
            ],
            where: {
                customer_id: request.query.userId,
                country_id,
                rstatus: 1,
            },
        });
        // console.log('cartttdataaa from placeorder', cartData, )
        if (cartData) {
            const cartItems = cartData?.map((item) => item.dataValues);
            // console.log(cartItems, 'itemsmsmsm')
            return getPlaceOrderHelper(request, cartItems);
        } else return "notfound";
    } catch (err) {
        console.log("error from GetPlaceOrder service", err);
        throw err;
    }
};

export const GetDirectPlaceOrder = async (request) => {
    try {
        const cartData = request.body.cartIds
            .split(",")
            .filter(Boolean)
            .map((entry) => ({
                product_id: parseInt(entry.split("|")[0]),
                total_order: parseInt(entry.split("|")[1]),
                vendor_id: parseInt(entry.split("|")[3]),
                traking_action: "add",
            }));
        // console.log('cartttdataaa', cartData, request.body.cartIds)
        return getPlaceOrderHelper(request, cartData);
    } catch (err) {
        console.log("error from GetDirectPlaceOrder service", err);
        throw err;
    }
};

const getPlaceOrderHelper = async (request, cartData) => {
    try {
        const { country_id } = request || request.body.country_id;
        const loginId = request.query.userId || request.user.user_id;
        const currentDate = currentDateTime();

        const ext_war_per = 5;
        let grand_total = 0;
        let final_total = 0;
        let sub_total = 0;
        let shipping_charge = 0;
        let free_shipping = 0;
        let clearence_count = 0;
        let clearencetotal = 0;
        let temp_count = 0;
        let tabby_type = 0;
        let offerProduct = 0;
        let credcardOnly = 0;
        let freeshippingvalue = 0;
        let categoryId = 0;
        let min_tempvalue = 0;
        let min_value = 0;
        let withvat = 0;
        let update_cart = [];
        let sub_free_shipping = 0;
        let showError = false;
        let msg = "";
        let hasFreeShippingProduct = false;
        // console.log(cartData, 'carttttttt')
        const product_array = [];
        cartData.forEach((ele) => {
            product_array.push({
                productId: ele.product_id,
                vendorId: ele.vendor_id,
            });
        });

        const productList = await getProductsByIdsElk(request, product_array);
        const final_array = cartData.map((cart) =>
            Object.assign(
                cart,
                productList.find((product) => product.id === cart.product_id)
            )
        );
        // console.log('final araaay', final_array)
        await Promise.all(
            final_array.map(async (element) => {
                let subCatId = element.subcategory_id;
                categoryId = element.category_id;

                const date = moment(currentDate).unix();
                let from_date = "0";
                let to_date = "0";
                let qty = 0;

                if (element.from_date != "0000-00-00 00:00:00") {
                    from_date = moment(element.from_date).unix();
                }
                if (element.to_date != "0000-00-00 00:00:00") {
                    to_date = moment(element.to_date).unix();
                }
                if (date > from_date && date < to_date) {
                    offerProduct = 1;
                }

                //making free shipping for mobile category
                if (FREE_SHIPPING_SUBCATEGORIES.includes(subCatId)) {
                    hasFreeShippingProduct = true;
                    sub_free_shipping++;
                }

                if (element.traking_action.toLowerCase() == "add") {
                    update_cart.push(element.cart_id);
                }

                const subCatData = await getSubCategoryById(subCatId);
                if (subCatData != "error") {
                    if (subCatData.tabby_type == 1) tabby_type = 1;
                }

                // if (
                //     element.quantity > 0 &&
                //     element.quantity <= element.total_order
                // ) {
                //     qty = element.quantity;
                // } else {
                qty = element.total_order;
                // }
                // if (element.quantity <= 0) qty = 0;

                let promotion_charge = 0;
                let promotion_from = moment(element.from_date).unix();
                let promotion_to = moment(element.to_date).unix();
                let price = 0;

                if (
                    parseInt(element.promotion_price) > 0 &&
                    date > promotion_from &&
                    date < promotion_to
                ) {
                    price = element.promotion_price;
                    promotion_charge++;
                } else if (parseInt(element.special_price) > 0) {
                    price = element.special_price;
                } else price = element.price;

                const temp_product = await getProductsBySectionElk(request, {
                    section_id: 53,
                    product_id: element.id,
                    offset: 0,
                    limit: 1,
                });
                if (temp_product.length > 0) {
                    min_tempvalue = 0.0;
                    temp_count++;
                }

                const clearance_product = await getProductsBySectionElk(
                    request,
                    {
                        section_id: 51,
                        product_id: element.id,
                        offset: 0,
                        limit: 1,
                    }
                );
                if (clearance_product.length > 0) {
                    clearencetotal += price * element.total_order;
                    clearence_count++;
                }

                const total = qty * price;

                //Warranty price
                let wprice = 0;
                let totalWprice = 0;
                if (element.warranty == 1) {
                    wprice = (price * ext_war_per) / 100;
                    totalWprice = parseFloat(qty * wprice);
                }

                //Assigning minimum purchase and minimum free shipping value.
                if (shipping_charge < element.shipping_charge) {
                    shipping_charge = element.shipping_charge;
                }

                if (element.shipping_charge === 0) {
                    if (date > promotion_from && date < promotion_to) {
                        free_shipping++;
                        freeshippingvalue = 499.0;
                        min_tempvalue = 0.0;
                        min_value = min_tempvalue;
                    }
                }

                sub_total =
                    parseFloat(sub_total) +
                    parseFloat(total) +
                    parseFloat(totalWprice);
                return { sub_total };
            })
        );

        let minpur = min_value;

        // payment type 9 is from oms_payment_methods table if in future
        // we change the type we can just pass the value here
        const processing_fee = await getProcessingFees(9);

        const userDefaultAddress = await getUserDefaultAddress(loginId);
        if (userDefaultAddress?.id) {
            let freesuvalue = "";
            let areaID = userDefaultAddress.area;
            const areaShipping = await getShippingChargeFromArea(areaID);
            // console.log(areaShipping, 'areshippipihib')
            if (areaShipping > 0 && shipping_charge >= 50) {
                freesuvalue = freeshippingvalue;
                shipping_charge = areaShipping;
            } else if (free_shipping > 0 && clearence_count > 0) {
                freesuvalue = freeshippingvalue + clearencetotal;
                shipping_charge = parseInt(country_id) === 1 ? 10 : 1;
                if (sub_total > freesuvalue) {
                    freesuvalue = 0;
                    shipping_charge = 0;
                }
            } else if (free_shipping > 0 && sub_total < freeshippingvalue) {
                freesuvalue = freeshippingvalue;
                shipping_charge = parseInt(country_id) === 1 ? 10 : 1;
            } else if (sub_total >= freeshippingvalue && free_shipping > 0) {
                shipping_charge = shipping_charge;
                freesuvalue = freeshippingvalue;
            } else {
                shipping_charge = shipping_charge;
                freesuvalue = freeshippingvalue;
            }
        }

        // free shipping for mobile fest
        if (
            sub_free_shipping > 0 &&
            sub_total > FREE_SHIPPING_SUBTOTAL_VALUE.mobileFest[country_id]
        ) {
            shipping_charge = 0;

            //sub category id 183 = Television & Accessories
        } else if (sub_free_shipping > 0 && hasFreeShippingProduct) {
            shipping_charge = 0;
        }

        // free shipping for Eid clearance sale
        if (
            sub_total >=
            FREE_SHIPPING_SUBTOTAL_VALUE.eidClearanceSale[country_id]
        ) {
            shipping_charge = 0;
        }

        const donation = parseInt(country_id) === 1 ? 1 : 0;

        //Calculating Order total With Vat.
        grand_total =
            parseFloat(sub_total) +
            parseFloat(shipping_charge) +
            parseFloat(processing_fee);

        //Adding vat amount
        withvat = (grand_total * VAT[country_id]) / (100 + VAT[country_id]);

        final_total = grand_total;

        let selected_processing = "";

        // category id 157 = pre owned
        if (
            !(
                categoryId === 157 ||
                (final_total < SHOW_COD_FINAL_TOTAL_VALUE[country_id] &&
                    credcardOnly === 0)
            )
        ) {
            selected_processing = "";
        } else {
            switch (parseInt(country_id)) {
                //This is the condition for UAE, Oman and Bahrain
                case 1:
                case 2:
                case 6:
                    if (final_total < PROCESSING_FEE_COD[country_id][0].max) {
                        let processing = Math.round(
                            (final_total * 2) / 100
                        ).toFixed(2);
                        if (final_total < 100) {
                            processing =
                                PROCESSING_FEE_COD[country_id][0].value;
                        }

                        selected_processing = processing;
                    } else {
                        selected_processing =
                            parseInt(country_id) === 1 ? "10.00" : "1.00";
                    }

                    break;

                // This is for Qatar and Kuwait
                case 3:
                case 5:
                    for (const tier of PROCESSING_FEE_COD[country_id]) {
                        if (final_total <= tier.max) {
                            selected_processing = tier.value;
                            break;
                        }
                    }

                    if (selected_processing === "") {
                        selected_processing =
                            parseInt(country_id) === 3 ? 10 : "1.00";
                    }

                    if (parseInt(country_id) === 3 && final_total > 500) {
                        selected_processing = "10";
                    }
                    break;

                default:
                    selected_processing = "";
            }
        }
        const currency =
            parseInt(country_id) === 5 ? "KD" : CURRENCY[country_id].currency;
        const allPaymnetMethods = {
            COD: {
                id: "payment_method",
                payment_method: "cash",
                label: "Cash On Delivery",
                sub_label: `Processing Fees: ${selected_processing} ${currency}`,
                processing_fee: addZeroes(selected_processing),
                type: "cashondelivery",
                selected: true,
            },
            Payfort: {
                id: "payment_method1",
                payment_method: "credit_payfort",
                label: "Pay by Card",
                sub_label: "Processing Fees: 0.00 " + currency,
                processing_fee: addZeroes(0),
                type: "plan_credit",
                selected: true,
            },
            Tabby: {
                id: "payment_method5",
                payment_method: "tabby",
                label: "TABBY - FREE INSTALLMENT  DEBIT CARD ACCEPTED",
                sub_label: "Processing Fees: 0.00 " + currency,
                processing_fee: addZeroes(0), // actual data= 20.00
                selected: false,
                type: "tabby_debit",
                image: process.env.images + "tabby-badge.png",
            },
        };

        const paymentMethods = [];

        let showCOD = selected_processing !== "";

        if (parseInt(country_id) === 1) {
            const codBlockLimit = process.env.COD_LIMIT || 100;
            showCOD = selected_processing !== "" && codBlockLimit > final_total;
            // codBlockLimit <= 100;
        }

        if (showCOD) {
            paymentMethods.push(allPaymnetMethods["COD"]);
        }

        paymentMethods.push(allPaymnetMethods["Payfort"]);

        // adding tabby in UAE and Kuwait only
        if (parseInt(country_id) === 1 || parseInt(country_id) === 5) {
            paymentMethods.push(allPaymnetMethods["Tabby"]);
        }

        if (update_cart.length > 0) {
            await updateCartTrackingMuliple(loginId, update_cart);
        }

        //checking clearence sale
        if (clearencetotal > 0 && free_shipping > 0) {
            minpur = clearencetotal + min_value;
        } else {
            minpur = min_value;
        }

        let minpur_error = "";
        let button_value = "continue";

        if (sub_total < minpur) {
            minpur_error =
                "Minimum purchase amount should be " +
                currency +
                minpur +
                " & above";
        } else {
            button_value = "order";
        }

        if (final_array.length >= 1) {
            let distinctSaleProductsCount = 0;
            final_array.forEach((product) => {
                // console.log('Product Price - ', product.price, 'special_price', product.special_price, 'promotion price', product.promotion_price)
                if (
                    product.price === 1 ||
                    product.special_price === 1 ||
                    product.promotion_price === 1
                ) {
                    ++distinctSaleProductsCount;
                    // console.log('Show Error', product.price)
                    if (
                        product.total_order >
                        process.env.SUMMER_SALE_MAX_ONE_PRODUCT_QUANTITY
                    ) {
                        showError = true;
                        msg = `Max ${process.env.SUMMER_SALE_MAX_ONE_PRODUCT_QUANTITY} quantity allowed for each 1 ${currency} item per order.`;
                        return;
                    }
                    if (
                        distinctSaleProductsCount >
                        process.env.SUMMER_SALE_MAX_DISTINCT_PRODUCTS_COUNT
                    ) {
                        showError = true;
                        msg = `You can only add up to ${process.env.SUMMER_SALE_MAX_DISTINCT_PRODUCTS_COUNT} items priced at 1 ${currency} per order.`;
                        return;
                    }
                    if (
                        final_total <
                        FREE_SHIPPING_SUBTOTAL_VALUE.eidClearanceSale[
                            country_id
                        ]
                    ) {
                        showError = true;
                        msg = `Almost there! Reach ${FREE_SHIPPING_SUBTOTAL_VALUE.eidClearanceSale[country_id]} ${currency} to complete your order.`;
                        return;
                    }
                }
            });
        }

        return {
            sub_total: `${currency} ${addZeroes(sub_total)}`,
            processing_fee: `${currency} ${addZeroes(processing_fee)}`,
            shipping_charge: `${currency} ${addZeroes(shipping_charge)}`,
            final_total: `${currency} ${addZeroes(final_total)}`,
            withvat: addZeroes(withvat),
            payment_method: paymentMethods,
            tabbyType: tabby_type,
            donation,
            button_value,
            minpur_error,
            showError,
            msg,
        };
    } catch (err) {
        console.log("error from getPlaceOrderHelper service", err);
        throw err;
    }
};

const getAddressById = async (address_id) => {
    try {
        const userAddress = await CrmCustomerAddress.findOne({
            where: { id: address_id },
        });
        return userAddress?.dataValues;
    } catch (err) {
        console.log("error from getAddressById service", err);
        throw err;
    }
};

const getGatewayType = async (type, country_id) => {
    try {
        const gatewayType = await OmsPaymentMethod.findOne({
            where: {
                value: type,
                country_id,
            },
        });
        // console.log(gatewayType.dataValues, "datewatTypeeews");
        return gatewayType?.dataValues;
    } catch (err) {
        console.log("error from getGatewayType service", err);
        throw err;
    }
};

export const postPlaceOrder = async (request) => {
    try {
        request.user_id = request.user.user_id;

        const { submitOrderData } = await processOrderHelper(request, false);
        const fakeReq = { body: submitOrderData };

        const orderResponse = await processOrder(fakeReq, true);

        if (orderResponse.status === "error") {
            throw {
                status: orderResponse.status,
                message: orderResponse.message,
            };
        }
        return orderResponse;
    } catch (err) {
        console.log("error from postPlaceOrder service", err);
        throw err;
    }
};

export const directBuy = async (request) => {
    try {
        request.body.cartIds = request.body.cartIds ? request.body.cartIds : "";
        const { submitOrderData } = await processOrderHelper(request, true);
        const fakeReq = { body: submitOrderData };

        const orderResponse = await processOrder(fakeReq, false);

        if (orderResponse.status === "error") {
            throw {
                status: orderResponse.status,
                message: orderResponse.message,
            };
        }
        return orderResponse;
    } catch (err) {
        console.log("error from directBuy service", err);
        throw err;
    }
};

const processOrderHelper = async (request, skipDB) => {
    try {
        const loginId = request.user.user_id;
        const currentDate = currentDateTime();

        const { country_id } = request || request.body.country_id;

        const {
            coupon_code,
            notes,
            payment_method,
            processing_fee,
            order_from = "Web",
            vendor_id = 0,
        } = request.body;

        request.body.notes = request.body.notes || "";
        request.body.donation_fee = request.body.donation_fee || 0;
        request.body.donation_fee = parseInt(request.body.donation_fee);
        request.body.cartIds = request.body.cartIds.replaceAll("|", "@");

        const { pay_type, installment } = getPayType(payment_method);
        // const rstatus = payment_method === "cash" ? "Pending" : "On Process";

        // Get user address
        const addressInfo = await getAddressById(request.body.addressId);
        if (!addressInfo?.name) throw new Error("User address not found");

        const {
            name: username_address,
            mobile,
            emirate = "",
            area,
            address,
            address2,
            city,
            latitude,
            longitude,
            building_name,
            company,
        } = addressInfo;

        request.body.area = area;

        // Get user profile
        const getMyProfile = await getMyProfileService(request);
        if (!getMyProfile?.result.length)
            throw new Error("User profile not found");

        const {
            email: user_email,
            gender,
            nationality,
        } = getMyProfile.result[0];

        // Generate order_ref_code
        const refCode = await incrementOrderRefCode();

        // Calculate amount using the provided calculation function
        const productAmount = await CalCulateOrderAmount(request, skipDB);
        console.log("productAmount :", productAmount);
        const {
            shipping_charges,
            final_total: total_amount,
            tabbyType,
            withvat: vat,
            sub_total,
            discount,
            promotionid,
        } = productAmount;

        const shippingAddress = {
            customer_id: loginId,
            name: username_address,
            emirate,
            mobile,
            area,
            address,
            address2,
            city,
            building_name,
            company,
            latitude,
            longitude,
            customer_addressid: request.body.addressId,
        };

        const userDetails = {
            name: username_address,
            gender,
            code: countryPhonePrefixes[country_id],
            mobile,
            nationality,
        };

        const shippingaddress = JSON.stringify(shippingAddress);
        const customer_details = JSON.stringify(userDetails);

        const cleanNotes = notes ? notes.replace(/\s/g, "") : "";
        const code = parseInt(discount) === 0 ? null : coupon_code;

        const gatewayType = await getGatewayType(payment_method, country_id);

        const order = await OmsOrders.create({
            type: "order",
            vendor_id,
            country_id,
            order_ref_code: refCode,
            customer_contact: mobile,
            order_date: currentDate,
            promotionid,
            promo_code: code,
            discount_amount: discount,
            sub_total,
            tax_amount: vat,
            shipping_charges,
            processing_fee,
            total_amount,
            payment_method,
            payment_methodid: pay_type,
            gway_paymentmethod: gatewayType?.id,
            payment_status: "Due", // Due for new orders, Paid after PG confirmation, remains Due for COD
            shippingaddress,
            // notes: cleanNotes,      // removed notes as we are not using them
            same_day_delivery: 0, // adding 0 to match with CRM orders but its not need
            customerid: loginId,
            orderfrom: order_from,
            customer_details,
            order_from_id: ORDER_FROM_ID[order_from],
            donation_fee: request.body.donation_fee,
            order_statusid: 0, // 0 for new orders, 1 for confirmed orders
        });

        // Prepare submission data
        const cityName = await AdminCountryEmirates.findOne({
            where: { emirateid: emirate },
        });
        const fullAddress = `${address}, ${address2}`;

        const submitOrderData = {
            ip_address: 0,
            cartids: request.body.cartIds || "",
            ourshopee_order_id: refCode,
            totalAmount: total_amount,
            email: user_email,
            name: username_address,
            city: cityName.emirate,
            address: fullAddress,
            userId: loginId,
            payment_method,
            tabbyType,
            mobile,
            country_id,
        };

        return { submitOrderData, order_id: order.orderid };
    } catch (err) {
        console.log("error from processOrderHelper service", err);
        throw err;
    }
};

const CalCulateOrderAmount = async (request, skipDB) => {
    try {
        const { country_id } = request || request.body.country_id;
        const currentDate = currentDateTime();
        const user_id = request.user.user_id;

        const ext_war_per = 5;
        let grand_total = 0;
        let final_total = 0;
        let sub_total = 0;
        let shipping_charge = 0;
        let free_shipping = 0;
        let clearence_count = 0;
        let clearencetotal = 0;
        let tabby_type = 0;
        let freeshippingvalue = 0;
        // let categoryId = 0;
        let min_tempvalue = 0;
        // let min_value = 0;
        let update_cart = [];
        let sub_free_shipping = 0;
        let hasFreeShippingProduct = false;
        let cartData;

        // skip DB call in case of direct buy api as no data was
        // saved in the DB so there's nothing to fetch
        if (skipDB) {
            cartData = request.body.cartIds
                .split(",")
                .filter(Boolean)
                .map((entry) => ({
                    product_id: parseInt(entry.split("@")[0]),
                    total_order: parseInt(entry.split("@")[1]),
                    vendor_id: parseInt(entry.split("@")[3]),
                    traking_action: "add",
                }));
        } else {
            cartData = await OmsCartTracking.findAll({
                attributes: [
                    ["track_id", "cart_id"],
                    "product_id",
                    "total_order",
                    "add_date",
                    "vendor_id",
                    ["action", "traking_action"],
                ],
                where: {
                    customer_id: user_id,
                    country_id,
                    rstatus: 1,
                },
            });
            cartData = cartData.map((item) => item.dataValues);
            // console.log('cartdaatatata', cartData)
        }
        console.log("cartData :", cartData);
        const product_array = [];
        cartData.forEach((ele) => {
            console.log("ele :", ele);
            product_array.push({
                productId: ele.product_id,
                vendorId: ele.vendor_id,
            });
        });
        const productList = await getProductsByIdsElk(request, product_array);
        console.log("productList :", productList);
        const final_array = cartData.map((cart) =>
            Object.assign(
                cart,
                productList.find((product) => product.id === cart.product_id)
            )
        );
        // console.log(final_array, 'final araaaayyaya')
        await Promise.all(
            final_array.map(async (element) => {
                let subCatId = element.subcategory_id;
                let categoryId = element.category_id;
                let qty = 0;

                const date = moment(currentDate).unix();
                let from_date = "0";
                let to_date = "0";

                if (element.from_date != "0000-00-00 00:00:00") {
                    from_date = moment(element.from_date).unix();
                }
                if (element.to_date != "0000-00-00 00:00:00") {
                    to_date = moment(element.to_date).unix();
                }
                if (date > from_date && date < to_date) {
                    offerProduct = 1;
                }

                //making free shipping for mobile category
                if (FREE_SHIPPING_SUBCATEGORIES.includes(subCatId)) {
                    hasFreeShippingProduct = true;
                    sub_free_shipping++;
                }

                if (element.traking_action.toLowerCase() == "add") {
                    update_cart.push(element.cart_id);
                }

                const subCatData = await getSubCategoryById(subCatId);
                if (subCatData != "error") {
                    if (subCatData.tabby_type == 1) tabby_type = 1;
                }

                // if (
                //     element.quantity > 0 &&
                //     element.quantity <= element.total_order
                // ) {
                //     qty = element.quantity;
                // } else {
                qty = element.total_order;
                // }
                // if (element.quantity <= 0) qty = 0;

                // let promotion_charge = 0;
                let promotion_from = moment(element.from_date).unix();
                let promotion_to = moment(element.to_date).unix();
                let price = 0;

                if (
                    parseInt(element.promotion_price) > 0 &&
                    date > promotion_from &&
                    date < promotion_to
                ) {
                    price = element.promotion_price;
                    // promotion_charge++
                } else if (parseInt(element.special_price) > 0) {
                    price = element.special_price;
                } else price = element.price;

                const temp_product = await getProductsBySectionElk(request, {
                    section_id: 53,
                    product_id: element.id,
                    offset: 0,
                    limit: 1,
                });
                if (temp_product.length > 0) {
                    min_tempvalue = 0.0;
                    // temp_count++;
                }

                const clearance_product = await getProductsBySectionElk(
                    request,
                    {
                        section_id: 51,
                        product_id: element.id,
                        offset: 0,
                        limit: 1,
                    }
                );
                if (clearance_product.length > 0) {
                    clearencetotal += price * element.total_order;
                    clearence_count++;
                }

                const total = qty * price;

                //Warranty price
                let wprice = 0;
                let totalWprice = 0;
                if (element.warranty == 1) {
                    wprice = (price * ext_war_per) / 100;
                    totalWprice = parseFloat(qty * wprice);
                }

                //Assigning minimum purchase and minimum free shipping value.
                if (shipping_charge < element.shipping_charge) {
                    shipping_charge = element.shipping_charge;
                }

                if (element.shipping_charge === 0) {
                    if (date > promotion_from && date < promotion_to) {
                        free_shipping++;
                        freeshippingvalue = 499.0;
                        min_tempvalue = 0.0;
                        min_value = min_tempvalue;
                    }
                }

                sub_total =
                    parseFloat(sub_total) +
                    parseFloat(total) +
                    parseFloat(totalWprice);
                return { sub_total };
            })
        );

        // let minpur = min_value;

        let freesuvalue = "";
        let areaID = request.body.area;
        const areaShipping = await getShippingChargeFromArea(areaID);
        if (areaShipping > 0 && shipping_charge >= 50) {
            freesuvalue = freeshippingvalue;
            shipping_charge = areaShipping;
        } else if (free_shipping > 0 && clearence_count > 0) {
            freesuvalue = freeshippingvalue + clearencetotal;
            shipping_charge = parseInt(country_id) === 5 ? 1 : 10;
            if (sub_total > freesuvalue) {
                freesuvalue = 0;
                shipping_charge = 0;
            }
        } else if (free_shipping > 0 && sub_total < freeshippingvalue) {
            freesuvalue = freeshippingvalue;
            shipping_charge = parseInt(country_id) === 5 ? 1 : 10;
        } else if (sub_total >= freeshippingvalue && free_shipping > 0) {
            shipping_charge = shipping_charge;
            freesuvalue = freeshippingvalue;
        } else {
            shipping_charge = shipping_charge;
            freesuvalue = freeshippingvalue;
        }

        // free shipping for mobile fest
        if (
            sub_free_shipping > 0 &&
            sub_total > FREE_SHIPPING_SUBTOTAL_VALUE.mobileFest[country_id]
        ) {
            shipping_charge = 0;
        }
        //sub category id 183 = Television & Accessories
        else if (sub_free_shipping > 0 && hasFreeShippingProduct) {
            shipping_charge = 0;
        }

        // free shipping for Eid clearance sale
        if (
            sub_total >=
            FREE_SHIPPING_SUBTOTAL_VALUE.eidClearanceSale[country_id]
        ) {
            shipping_charge = 0;
        }

        let discount = 0;
        const donation = request.body.donation_fee || 0;
        const processingFee = request.body.processing_fee;

        //Calculating Order total With Vat.
        grand_total =
            parseFloat(sub_total) +
            parseFloat(shipping_charge) +
            parseFloat(processingFee);

        let checkDiscount;
        if (request.body.coupon_code) {
            checkDiscount = await checkCouponCode({
                country_id,
                user_id,
                coupon_code: request.body.coupon_code,
                tamount: grand_total,
            });
            // console.log(checkDiscount, 'discount')
            if (checkDiscount?.sucess) {
                discount = checkDiscount.discount;
            }
        }

        //Adding vat amount
        // this is the actual vat calculation but removing it for now to match production calculations
        // let withvat = (grand_total * VAT[country_id]) / (100 + VAT[country_id]);
        let withvat = (grand_total * VAT[country_id]) / 100;

        final_total = grand_total + donation - discount;

        return {
            sub_total: addZeroes(sub_total),
            processing_fee: addZeroes(processingFee),
            shipping_charges: addZeroes(shipping_charge),
            donation: addZeroes(donation),
            final_total: addZeroes(final_total),
            withvat: addZeroes(withvat),
            discount: addZeroes(discount),
            tabbyType: tabby_type,
            freesuvalue: freesuvalue,
            promotionid: checkDiscount?.promotionid || 0,
        };
    } catch (err) {
        console.log("error from CalCulateOrderAmount service", err);
        throw err;
    }
};

export const checkCouponCode = async (request) => {
    try {
        const { country_id } = request || request.body.country_id;
        const customerId = request.user_id || request.user.user_id;
        const promotion_code = request.coupon_code || request.body.coupon;
        const total_amount = request.tamount || request.body.tamount;
        const currency = CURRENCY[country_id].currency;
        const today = new Date();

        // const hardCodedField = `${COUNTRY_NAMES[country_id]}_COUPON_CODE`
        // const hardCodedCouponCode = process.env[hardCodedField]

        // // hardcoded county specific coupon codes until inplementation in CRM
        // if (!(hardCodedCouponCode === promotion_code)) {
        //      return {
        //         coupon_code : promotion_code,
        //         msg : "Invalid Coupon Code"
        //     }
        // }

        // find promotion code details
        const getPromotionDetails = await CatalogPromotion.findOne({
            where: { promo_code: promotion_code, country_id, rstatus: 1 },
        });

        // console.log('get Promotion', getPromotionDetails.dataValues, customerId)

        if (!getPromotionDetails) {
            return {
                coupon_code: promotion_code,
                msg: "Invalid Coupon Code",
            };
        }

        const startDate = new Date(getPromotionDetails.dataValues.startdate);
        const endDate = new Date(getPromotionDetails.dataValues.enddate);

        if (today > endDate || today < startDate) {
            return {
                coupon_code: promotion_code,
                msg: "Invalid Coupon Code",
            };
        }

        if (total_amount < getPromotionDetails.dataValues.min_order_value) {
            return {
                coupon_code: promotion_code,
                msg: `Add ${currency} ${getPromotionDetails.dataValues.min_order_value - total_amount} more to reach the ${currency} ${getPromotionDetails.dataValues.min_order_value} minimum for this coupon.`,
            };
        }

        const count = await OmsOrders.count({
            where: {
                order_date: {
                    [Op.between]: [startDate, endDate],
                },
                customerid: customerId,
                promo_code: getPromotionDetails.dataValues.promo_code,
            },
        });

        if (count >= getPromotionDetails.dataValues.max_usa_user) {
            return {
                coupon_code: promotion_code,
                msg: `You've used this coupon ${getPromotionDetails.dataValues.max_usa_user} times already. Try our other deals!`,
            };
        }

        return {
            discount: getPromotionDetails.dataValues.deduction_amount,
            coupon_code: promotion_code,
            msg: "Coupon validated successfully",
            sucess: true,
            promotionid: getPromotionDetails.dataValues.promotionid,
        };
    } catch (err) {
        console.log("error from checkCouponCode service", err);
        throw err;
    }
};

const getPayType = (payment_method) => {
    let pay_type = 0;
    let installment = 0;
    switch (payment_method) {
        case "cash":
            pay_type = 2;
            break;
        case "credit_payfort":
            pay_type = 1;
            break;
        case "credit_network":
            pay_type = 1;
            break;
        case "credit_ccavenue":
            pay_type = 1;
            break;
        case "instalment":
            pay_type = 4;
            break;
        case "tabby":
            pay_type = 1;
            break;
        case "cashew":
            pay_type = 6;
            break;
        case "tamara":
            pay_type = 5;
            break;
        case "Checkout":
            pay_type = 8;
            break;
        case "Tap":
            pay_type = 9;
            break;
        case "PostPay":
            pay_type = 10;
            break;
        case "tabby-installment":
            pay_type = 11;
            installment = 1;
            break;
    }
    return { pay_type, installment };
};

// const generateOrderId = async () => {
//     var or_id = await OmsOrders.max('orderid');
//     const characters1 = 'ABCDEFGHIJKLMNO';
//     const characters2 = 'PQRSTUVWXYZ';

//     var or = or_id + 1;
//     var r = Math.floor(100 + Math.random() * 1000);
//     var aa = characters1.charAt(Math.floor(Math.random() * characters1.length));
//     var aaa = characters2.charAt(Math.floor(Math.random() * characters2.length));

//     order_id1 = or + aaa + r + aa;
//     return order_id1;
// };

const incrementOrderRefCode = async () => {
    let currentRefCode = await OmsOrders.findOne({
        attributes: ["order_ref_code"],
        order: [["orderid", "DESC"]],
    });
    currentRefCode = currentRefCode?.order_ref_code || null;

    const order_ref_code = currentRefCode.replace(/-\d+$/, "");

    const numbers = order_ref_code.replace(/[^0-9]/g, "");
    const letters = order_ref_code.replace(/[^a-zA-Z]/g, "");

    let ord_num, ord_alp;

    if (letters && numbers) {
        if (parseInt(numbers) > 10000) {
            // Increment alphabet part
            ord_alp = incrementLetters(letters);
            ord_num = 1001;
        } else {
            ord_num = parseInt(numbers) + 1;
            ord_alp = letters;
        }
    } else {
        if (parseInt(numbers) < 1001 || parseInt(numbers) > 10000) {
            ord_num = 1001;
        } else {
            ord_num = parseInt(numbers) + 1;
        }
        ord_alp = "A";
    }

    return `${ord_alp}${ord_num}`;
};

// Helper to increment letter part like A → B, Z → AA, etc.
const incrementLetters = (str) => {
    str = str.toUpperCase();
    let carry = 1;
    let result = "";

    for (let i = str.length - 1; i >= 0; i--) {
        let code = str.charCodeAt(i) - 65 + carry; // 'A' = 65
        carry = 0;

        if (code >= 26) {
            code -= 26;
            carry = 1;
        }

        result = String.fromCharCode(65 + code) + result;
    }

    if (carry) {
        result = "A" + result;
    }

    return result;
};

const addZeroes = function (num) {
    return num.toLocaleString("en", {
        useGrouping: false,
        minimumFractionDigits: 2,
    });
};
