import CrmRatings from "../models/crm_ratings.model.js";
import CrmCustomer from "../models/crm_customer.model.js";
import jwt from "jsonwebtoken";
import { Op } from "sequelize";
import CrmRatingVotes from "../models/crm_rating_votes.model.js";
import { RATING_VOTES, REVIEW_STATUS } from "../region/config.js";

// **************** Authorization Not required ****************
// Get reviews of a product
export const getProductReviewsByProductId = async (req) => {
    try {
        const { productid: product_id } = req.params;
        let {
            page = 1,
            sort = "newest",
            search = "",
            stars,
            limit = 5,
        } = req.query;
        page = parseInt(page, 10) || 1;
        limit = parseInt(limit, 10) || 5;

        const offset = (page - 1) * limit;

        if (!product_id) {
            return {
                statusCode: 200,
                message: "Product ID is required",
                status: "failure",
                result: [],
            };
        }

        const safeParseArray = (data) => {
            try {
                if (Array.isArray(data)) return data;
                if (typeof data === "string") return JSON.parse(data);
                return [];
            } catch {
                return [];
            }
        };

        let user;
        const token =
            req.cookies?.accessToken ||
            req.headers?.authorization?.replace("Bearer ", "");
        if (token) {
            try {
                const decodedToken = jwt.verify(
                    token,
                    process.env.TOKEN_SECRET
                );
                user = await CrmCustomer.findByPk(decodedToken?.user_id);
            } catch (error) {
                console.log("error verifying token >>>>>>>>", error);
            }
        }

        let userReview;
        if (user) {
            userReview = await CrmRatings.findOne({
                where: {
                    product_id,
                    customer_id: user.id,
                    rstatus: {
                        [Op.in]: [
                            REVIEW_STATUS.ACCEPTED,
                            REVIEW_STATUS.PENDING,
                        ],
                    },
                },
            });
        }

        // Build where clause for filtered results (excluding logged-in user)
        let filteredWhereClause = {
            product_id,
            rstatus: REVIEW_STATUS.ACCEPTED,
            ...(user && { customer_id: { [Op.ne]: user.id } }),
        };

        // Handle multiple star ratings for checkbox filtering
        let selectedStarArray = null;
        if (stars) {
            let starArray;

            if (typeof stars === "string") {
                if (stars.includes(",")) {
                    starArray = stars
                        .split(",")
                        .map((s) => parseInt(s.trim(), 10))
                        .filter((s) => !isNaN(s) && s >= 1 && s <= 5);
                } else {
                    const singleStar = parseInt(stars, 10);
                    if (
                        !isNaN(singleStar) &&
                        singleStar >= 1 &&
                        singleStar <= 5
                    ) {
                        starArray = [singleStar];
                    }
                }
            } else if (Array.isArray(stars)) {
                starArray = stars
                    .map((s) => parseInt(s, 10))
                    .filter((s) => !isNaN(s) && s >= 1 && s <= 5);
            }

            if (starArray && starArray.length > 0) {
                selectedStarArray = starArray;
                filteredWhereClause.rating = { [Op.in]: starArray };
            }
        }

        if (search) {
            filteredWhereClause = {
                ...filteredWhereClause,
                [Op.or]: [
                    { "$user.first_name$": { [Op.like]: `%${search}%` } },
                    { review_title: { [Op.like]: `%${search}%` } },
                ],
            };
        }

        let order = [];

        if (selectedStarArray && selectedStarArray.length > 1) {
            order.push(["rating", "DESC"]);

            if (sort === "newest") {
                order.push(["created_at", "DESC"]);
            } else if (sort === "oldest") order.push(["created_at", "ASC"]);
            else if (sort === "lowest") {
                order = [["rating", "ASC"]];
            }
        } else {
            if (sort === "newest") {
                order = [["created_at", "DESC"]];
            } else if (sort === "oldest") order = [["created_at", "ASC"]];
            else if (sort === "highest") order = [["rating", "DESC"]];
            else if (sort === "lowest") order = [["rating", "ASC"]];
        }

        // Get filtered results for pagination
        const { rows: reviews, count: totalFilteredReviews } =
            await CrmRatings.findAndCountAll({
                where: filteredWhereClause,
                include: [
                    { model: CrmCustomer, as: "user" },
                    {
                        model: CrmRatingVotes,
                        as: "votes",
                    },
                ],
                order,
                limit,
                offset,
            });

        // Get all product ratings for overall stats (unfiltered)
        const totalProductsRating = await CrmRatings.findAll({
            where: { product_id, rstatus: REVIEW_STATUS.ACCEPTED },
        });

        if ((!reviews || reviews.length === 0) && !userReview) {
            return {
                statusCode: 200,
                message: "No reviews found for the given product",
                status: "success",
                result: [],
                stats: {
                    totalratingsForPagination: 0,
                    currentPageForPagination: page,
                    totalPages: 0,
                    totalRatingCount: totalProductsRating.length,
                    averageRating: 0,
                    ratingCount: {
                        1: { value: 0, percentage: 0 },
                        2: { value: 0, percentage: 0 },
                        3: { value: 0, percentage: 0 },
                        4: { value: 0, percentage: 0 },
                        5: { value: 0, percentage: 0 },
                    },
                    reviewCount: 0,
                },
            };
        }

        // Helper function to check if a user is a trusted reviewer
        const checkTrustedReviewer = async (customer_id) => {
            const reviewCount = await CrmRatings.count({
                where: {
                    customer_id,
                    rstatus: REVIEW_STATUS.ACCEPTED,
                },
            });
            return reviewCount >= 3;
        };

        // Get unique customer IDs from reviews for trusted reviewer check
        const customerIds = reviews.map((review) => review.customer_id);
        if (userReview) {
            customerIds.push(userReview.customer_id);
        }
        const uniqueCustomerIds = [...new Set(customerIds)];

        // Check trusted status for all unique customers
        const trustedReviewersMap = {};
        for (const customerId of uniqueCustomerIds) {
            trustedReviewersMap[customerId] =
                await checkTrustedReviewer(customerId);
        }

        const plainReviews = reviews.map((review) => {
            console.log("review :", review);
            const plain = review.get({ plain: true });
            plain.likes = safeParseArray(plain.likes);
            plain.dislikes = safeParseArray(plain.dislikes);
            // Add trusted reviewer status
            plain.isTrustedReviewer =
                trustedReviewersMap[plain.customer_id] || false;
            return plain;
        });

        let plainUserReview = null;
        if (userReview) {
            plainUserReview = userReview.get({ plain: true });
            plainUserReview.likes = safeParseArray(plainUserReview.likes);
            plainUserReview.dislikes = safeParseArray(plainUserReview.dislikes);
            // Add trusted reviewer status for user's own review
            plainUserReview.isTrustedReviewer =
                trustedReviewersMap[plainUserReview.customer_id] || false;
        }

        // Search relevance sorting
        if (search) {
            plainReviews.sort((a, b) => {
                const nameA = a.user?.first_name?.toLowerCase() || "";
                const nameB = b.user?.first_name?.toLowerCase() || "";
                const titleA = a.review?.toLowerCase() || "";
                const titleB = b.review?.toLowerCase() || "";
                const searchLower = search.toLowerCase();

                const nameMatchA = nameA.includes(searchLower) ? 1 : 0;
                const nameMatchB = nameB.includes(searchLower) ? 1 : 0;
                const titleMatchA = titleA.includes(searchLower) ? 1 : 0;
                const titleMatchB = titleB.includes(searchLower) ? 1 : 0;

                // If multi-star filter is applied, prioritize rating first
                if (selectedStarArray && selectedStarArray.length > 1) {
                    if (a.rating !== b.rating) {
                        return b.rating - a.rating;
                    }
                }

                if (nameMatchA !== nameMatchB) return nameMatchB - nameMatchA;
                if (titleMatchA !== titleMatchB)
                    return titleMatchB - titleMatchA;

                return 0;
            });
        }

        // Calculate overall stats (unfiltered data)
        const rawRatingCount = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
        let totalRatingCountForStats = 0;
        let sumAllRatings = 0;

        for (const ratingEntry of totalProductsRating) {
            const ratingVal = parseFloat(ratingEntry.rating);
            if (!isNaN(ratingVal)) {
                totalRatingCountForStats++;
                sumAllRatings += ratingVal;
                const rounded = Math.round(ratingVal);
                if (rounded >= 1 && rounded <= 5) {
                    rawRatingCount[rounded]++;
                }
            }
        }

        const ratingCount = {};
        for (let i = 1; i <= 5; i++) {
            const count = rawRatingCount[i];
            const percentage =
                totalRatingCountForStats > 0
                    ? Number(
                          ((count / totalRatingCountForStats) * 100).toFixed(2)
                      )
                    : 0;
            ratingCount[i] = { value: count, percentage };
        }

        const averageRatingAll =
            totalRatingCountForStats > 0
                ? sumAllRatings / totalRatingCountForStats
                : 0;

        const totalReviewCount = totalProductsRating.filter(
            (i) => i.review && i.review.trim() !== ""
        ).length;

        return {
            statusCode: 200,
            message: "Reviews fetched successfully",
            status: "success",
            result: plainReviews,
            userReview: plainUserReview,
            stats: {
                totalratingsForPagination: totalFilteredReviews,
                currentPageForPagination: page,
                totalPages: Math.ceil(totalFilteredReviews / limit),
                totalRatingCount: totalRatingCountForStats,
                averageRating: Number(averageRatingAll.toFixed(2)),
                ratingCount,
                reviewCount: totalReviewCount,
            },
        };
    } catch (err) {
        return {
            statusCode: 200,
            message: err.message || "Failed to fetch reviews.",
            status: "failure",
            result: [],
        };
    }
};

// *************** Authorization required ***************
// Create a review
export const addProductReview = async (req) => {
    try {
        const { vendor_id, orderno, productid, rating, review, review_title } =
            req.body;

        const COUNTRY_ID = req.country_id;

        const isAllowedForEveryone =
            process.env.PRODUCT_REVIEW_IS_ALLOWED_FOR_EVERYONE == "true";

        if (!isAllowedForEveryone && (!orderno || orderno === "0")) {
            return {
                statusCode: 200,
                message: "Order id is required of the product.",
                status: "failure",
                result: {},
            };
        }

        const customerid = req.user?.user_id;
        if (!customerid) {
            return {
                statusCode: 200,
                message: `Only logged in users can leave a review.`,
                status: "failure",
                result: {},
            };
        }

        // Validate required fields
        const missingFields = [];
        if (!productid) missingFields.push("productid");
        if (!rating) missingFields.push("rating");

        if (missingFields.length > 0) {
            return {
                statusCode: 200,
                message: `Missing required field(s): ${missingFields.join(", ")}`,
                status: "failure",
                result: {},
            };
        }

        // Validate rating is a number between 1 and 5
        const numericRating = parseFloat(rating);
        if (isNaN(numericRating) || numericRating < 1 || numericRating > 5) {
            return {
                statusCode: 200,
                message: "Rating must be a number between 1 and 5",
                status: "failure",
                result: {},
            };
        }

        // Validate user existence
        const isUser = await CrmCustomer.findOne({
            where: { id: customerid },
        });

        if (!isUser) {
            return {
                statusCode: 200,
                message: "User must be logged in before proceeding.",
                status: "failure",
                result: {},
            };
        }

        // Check if user already reviewed this product in this order
        const existingReview = await CrmRatings.findOne({
            where: {
                product_id: productid,
                customer_id: customerid,
                order_id: orderno || null,
            },
        });

        if (
            existingReview &&
            ![REVIEW_STATUS.SOFT_DELETED, REVIEW_STATUS.REJECTED].includes(
                existingReview.rstatus
            )
        ) {
            return {
                statusCode: 200,
                message: "You’ve already submitted a review for this product",
                status: "failure",
                result: {},
            };
        }

        // Prepare review data
        const reviewData = {
            country_id: COUNTRY_ID || 1,
            vendor_id: vendor_id || 0,
            order_id: orderno || null,
            product_id: productid,
            customer_id: customerid,
            rating: numericRating,
            review,
            review_title,
            image: req.filePaths,
            rstatus: REVIEW_STATUS.ACCEPTED,
        };

        const created = await CrmRatings.create(reviewData);

        return {
            statusCode: 201,
            message: "Review Submitted Successfully",
            status: "success",
            result: { ratings_id: created.ratings_id },
        };
    } catch (err) {
        console.log(err);
        return {
            statusCode: 200,
            message: err.message || "Failed to submit review.",
            status: "failure",
            result: {},
        };
    }
};

// Edit a review
export const editProductReview = async (req) => {
    try {
        const { rating, review, review_title, ratings_id, productid, images } =
            req.body;

        const customer_id = req.user?.user_id;

        const reviewInstance = await CrmRatings.findOne({
            where: { rating_id: ratings_id },
        });

        if (!reviewInstance) {
            return {
                statusCode: 200,
                message: "Review not found",
                status: "failure",
                result: {},
            };
        }

        if (reviewInstance.customer_id !== customer_id) {
            return {
                statusCode: 200,
                message: "Unauthorized access",
                status: "failure",
                result: {},
            };
        }

        if (reviewInstance.is_edited) {
            return {
                statusCode: 200,
                message: "You cannot edit this review again.",
                status: "failure",
                result: {},
            };
        }

        const missingFields = [];
        if (!ratings_id) missingFields.push("ratings_id");
        if (!productid) missingFields.push("productid");
        if (!rating) missingFields.push("rating");

        if (missingFields.length > 0) {
            return {
                statusCode: 200,
                message: `Missing required field(s): ${missingFields.join(", ")}`,
                status: "failure",
                result: {},
            };
        }

        const numericRating = parseFloat(rating);
        if (isNaN(numericRating) || numericRating < 1 || numericRating > 5) {
            return {
                statusCode: 200,
                message: "Rating must be a number between 1 and 5",
                status: "failure",
                result: {},
            };
        }
        let newArr;
        const safeImages = Array.isArray(images) ? images : [];

        if (!Array.isArray(req.filePaths)) {
            newArr = [...safeImages];
        } else {
            newArr = [...req.filePaths, ...safeImages];
        }

        if (newArr.length > 5) {
            return {
                statusCode: 200,
                message: "Only 5 images are allowed",
                status: "failure",
                result: {},
            };
        }

        await reviewInstance.update({
            rating: numericRating,
            review,
            review_title,
            image: newArr.length ? newArr : reviewInstance.old_images,
            rstatus: REVIEW_STATUS.PENDING,
            is_edited: true,
        });

        return {
            statusCode: 200,
            message: "Review has been updated.",
            status: "success",
            result: {},
        };
    } catch (err) {
        return {
            statusCode: 200,
            message: err.message || "Failed to update review.",
            status: "failure",
            result: {},
        };
    }
};

// Delete a review
export const deleteProductReview = async (req) => {
    try {
        const { ratings_id } = req.body;
        const customer_id = req.user?.user_id;

        const missingFields = [];
        if (!ratings_id) missingFields.push("ratings_id");

        if (missingFields.length > 0) {
            return {
                statusCode: 200,
                message: `Missing required field(s): ${missingFields.join(", ")}`,
                status: "failure",
                result: {},
            };
        }

        const reviewInstance = await CrmRatings.findOne({
            where: { rating_id: ratings_id },
        });

        if (!reviewInstance) {
            return {
                statusCode: 200,
                message: "Review not found",
                status: "failure",
                result: {},
            };
        }

        if (reviewInstance.customer_id !== customer_id) {
            return {
                statusCode: 200,
                message: "Unauthorized access",
                status: "failure",
                result: {},
            };
        }

        reviewInstance.rstatus = REVIEW_STATUS.SOFT_DELETED;
        await reviewInstance.save();

        return {
            statusCode: 200,
            message: "Review deleted successfully.",
            status: "success",
            result: {},
        };
    } catch (err) {
        return {
            statusCode: 200,
            message: err.message || "Failed to delete review.",
            status: "failure",
            result: {},
        };
    }
};

/**
 * Like a review
 * @param {*} req
 * @returns
 */
export const toggleLikeOnReview = async (req) => {
    try {
        const { review_id } = req.body;
        const user_id = req.user?.user_id;

        if (!user_id) {
            return {
                statusCode: 200,
                message: `Only logged in users can vote.`,
                status: "failure",
                result: {},
            };
        }

        if (!review_id) {
            return {
                statusCode: 200,
                message: "review_id is required",
                status: "failure",
                result: {},
            };
        }

        const review = await CrmRatings.findOne({
            where: { rating_id: review_id },
        });

        if (!review) {
            return {
                statusCode: 404,
                message: "Review not found",
                status: "failure",
                result: {},
            };
        }
        const existingVote = await CrmRatingVotes.findOne({
            where: {
                rating_id: review_id,
                customer_id: user_id,
            },
        });

        let action;
        let voteResult;

        if (existingVote) {
            if (existingVote.votes === RATING_VOTES.LIKE) {
                await existingVote.destroy();
                action = "removed";
                voteResult = "";
            } else {
                existingVote.votes = RATING_VOTES.LIKE;
                existingVote.updated_at = new Date();
                await existingVote.save();
                action = "changed to like";
                voteResult = RATING_VOTES.LIKE;
            }
        } else {
            await CrmRatingVotes.create({
                rating_id: review_id,
                customer_id: user_id,
                product_id: review.product_id,
                votes: RATING_VOTES.LIKE,
            });
            action = "added";
            voteResult = RATING_VOTES.LIKE;
        }

        //Update like and dislike count for the review
        updateReviewLikeDislikeCount(review_id);

        return {
            statusCode: 200,
            message: `Like ${action} successfully`,
            status: "success",
            result: {
                review_id,
                user_vote: voteResult,
                action: action,
            },
        };
    } catch (err) {
        return {
            statusCode: 200,
            message: err.message || "Something went wrong",
            status: "failure",
            result: {},
        };
    }
};

/**
 * Dislike a review
 * @param {*} req
 * @returns
 */
export const toggleDislikeOnReview = async (req) => {
    try {
        const { review_id } = req.body;
        const user_id = req.user?.user_id;

        if (!user_id) {
            return {
                statusCode: 200,
                message: "Only logged in users can vote.",
                status: "failure",
                result: {},
            };
        }

        if (!review_id) {
            return {
                statusCode: 200,
                message: "review_id is required",
                status: "failure",
                result: {},
            };
        }

        const review = await CrmRatings.findOne({
            where: { rating_id: review_id },
        });

        if (!review) {
            return {
                statusCode: 404,
                message: "Review not found",
                status: "failure",
                result: {},
            };
        }

        // Check if user has already voted
        const existingVote = await CrmRatingVotes.findOne({
            where: {
                rating_id: review_id,
                customer_id: user_id,
            },
        });

        let action;
        let voteResult;

        if (existingVote) {
            if (existingVote.votes === RATING_VOTES.DISLIKE) {
                await existingVote.destroy();
                action = "removed";
                voteResult = "";
            } else {
                existingVote.votes = RATING_VOTES.DISLIKE;
                existingVote.updated_at = new Date();
                await existingVote.save();
                action = "Changed to dislike";
                voteResult = RATING_VOTES.DISLIKE;
            }
        } else {
            await CrmRatingVotes.create({
                rating_id: review_id,
                customer_id: user_id,
                product_id: review.product_id,
                votes: RATING_VOTES.DISLIKE,
            });
            action = "added";
            voteResult = RATING_VOTES.DISLIKE;
        }

        //Update like and dislike count for the review
        updateReviewLikeDislikeCount(review_id);

        return {
            statusCode: 200,
            message: `Dislike ${action} successfully`,
            status: "success",
            result: {
                review_id,
                user_vote: voteResult,
                action: action,
            },
        };
    } catch (err) {
        return {
            statusCode: 200,
            message: err.message || "Something went wrong",
            status: "failure",
            result: {},
        };
    }
};

// Get all reviews of a user
export const getUserReviews = async (req) => {
    try {
        const customer_id = req.user?.user_id;

        if (!customer_id) {
            return {
                statusCode: 200,
                message: "Only logged in users can access their reviews.",
                status: "failure",
                result: [],
            };
        }

        const reviews = await CrmRatings.findAll({
            where: { customer_id, rstatus: REVIEW_STATUS.ACCEPTED },
            order: [["created_at", "DESC"]],
        });

        if (!reviews || reviews.length === 0) {
            return {
                statusCode: 200,
                message: "No reviews found for this user.",
                status: "failure",
                result: [],
            };
        }

        return {
            statusCode: 200,
            message: "User reviews fetched successfully.",
            status: "success",
            result: reviews,
        };
    } catch (err) {
        return {
            statusCode: 200,
            message: err.message || "Failed to fetch user reviews.",
            status: "failure",
            result: [],
        };
    }
};

/**
 * Helper function to get like/dislike counts for a review
 * @param {*} ratingId
 * @returns
 */
export const getReviewVoteCounts = async (ratingId) => {
    try {
        const [likesCount, dislikesCount] = await Promise.all([
            CrmRatingVotes.count({
                where: {
                    rating_id: ratingId,
                    votes: RATING_VOTES.LIKE,
                },
            }),
            CrmRatingVotes.count({
                where: {
                    rating_id: ratingId,
                    votes: RATING_VOTES.DISLIKE,
                },
            }),
        ]);

        return { likesCount, dislikesCount };
    } catch (err) {
        console.error("Error getting review vote counts:", err);
        return { likesCount: 0, dislikesCount: 0 };
    }
};

/**
 * Helper function to
 * Update like and dislike count for a review
 * @param {*} ratingId
 * @returns
 */
export const updateReviewLikeDislikeCount = async (ratingId) => {
    try {
        const { likesCount, dislikesCount } =
            await getReviewVoteCounts(ratingId);

        await CrmRatings.update(
            { likes: likesCount, dislikes: dislikesCount },
            { where: { rating_id: ratingId } }
        );

        console.log("Review vote counts updated successfully");
        return { likesCount, dislikesCount };
    } catch (err) {
        console.error("Error updating review vote counts:", err);
        return { likesCount: 0, dislikesCount: 0 };
    }
};
