import CrmRatings from "../models/crm_ratings.model.js";
import CrmCustomer from "../models/crm_customer.model.js";
import jwt from "jsonwebtoken";
import { Op } from "sequelize";
import { handleProductRatingUpdate } from "../elk/product-review.elk.js";

// **************** Authorization Not required ****************
// Get reviews of a product
export const getProductReviewsByProductId = async (req) => {
    try {
        const { productid } = req.params;
        let { page = 1, sort = "newest", search = "", stars } = req.query;
        page = parseInt(page, 10) || 1;
        const limit = 3;
        const offset = (page - 1) * limit;

        if (!productid) {
            return {
                statusCode: 200,
                message: "Product ID is required",
                status: "failure",
                result: [],
            };
        }

        const safeParseArray = (data) => {
            try {
                if (Array.isArray(data)) return data;
                if (typeof data === "string") return JSON.parse(data);
                return [];
            } catch {
                return [];
            }
        };

        let user;
        const token =
            req.cookies?.accessToken ||
            req.headers?.authorization?.replace("Bearer ", "");
        if (token) {
            try {
                const decodedToken = jwt.verify(
                    token,
                    process.env.TOKEN_SECRET
                );
                user = await CrmCustomer.findByPk(decodedToken?.user_id);
            } catch (error) {
                console.log("error verifying token >>>>>>>>", error);
            }
        }

        let userReview;
        if (user) {
            userReview = await CrmRatings.findOne({
                where: {
                    productid,
                    customerid: user.id,
                    rstatus: { [Op.in]: [0, 1] },
                },
            });
        }

        // Build where clause for filtered results (excluding logged-in user)
        let filteredWhereClause = {
            productid,
            rstatus: 1,
            ...(user && { customerid: { [Op.ne]: user.id } }),
        };

        // Handle multiple star ratings for checkbox filtering
        let selectedStarArray = null;
        if (stars) {
            let starArray;

            if (typeof stars === "string") {
                if (stars.includes(",")) {
                    starArray = stars
                        .split(",")
                        .map((s) => parseInt(s.trim(), 10))
                        .filter((s) => !isNaN(s) && s >= 1 && s <= 5);
                } else {
                    const singleStar = parseInt(stars, 10);
                    if (
                        !isNaN(singleStar) &&
                        singleStar >= 1 &&
                        singleStar <= 5
                    ) {
                        starArray = [singleStar];
                    }
                }
            } else if (Array.isArray(stars)) {
                starArray = stars
                    .map((s) => parseInt(s, 10))
                    .filter((s) => !isNaN(s) && s >= 1 && s <= 5);
            }

            if (starArray && starArray.length > 0) {
                selectedStarArray = starArray;
                filteredWhereClause.rating = { [Op.in]: starArray };
            }
        }

        if (search) {
            filteredWhereClause = {
                ...filteredWhereClause,
                [Op.or]: [
                    { "$user.first_name$": { [Op.like]: `%${search}%` } },
                    { review_title: { [Op.like]: `%${search}%` } },
                ],
            };
        }

        let order = [];

        if (selectedStarArray && selectedStarArray.length > 1) {
            order.push(["rating", "DESC"]);

            if (sort === "newest") {
                order.push(["rating_date", "DESC"]);
            } else if (sort === "oldest") order.push(["rating_date", "ASC"]);
            else if (sort === "lowest") {
                order = [["rating", "ASC"]];
            }
        } else {
            if (sort === "newest") {
                order = [["rating_date", "DESC"]];
            } else if (sort === "oldest") order = [["rating_date", "ASC"]];
            else if (sort === "highest") order = [["rating", "DESC"]];
            else if (sort === "lowest") order = [["rating", "ASC"]];
        }

        // Get filtered results for pagination
        const { rows: reviews, count: totalFilteredReviews } =
            await CrmRatings.findAndCountAll({
                where: filteredWhereClause,
                include: [{ model: CrmCustomer, as: "user" }],
                order,
                limit,
                offset,
            });

        // Get all product ratings for overall stats (unfiltered)
        const totalProductsRating = await CrmRatings.findAll({
            where: { productid, rstatus: 1 },
        });

        if ((!reviews || reviews.length === 0) && !userReview) {
            return {
                statusCode: 200,
                message: "No reviews found for the given product",
                status: "success",
                result: [],
                stats: {
                    totalratingsForPagination: 0,
                    currentPageForPagination: page,
                    totalPages: 0,
                    totalRatingCount: totalProductsRating.length,
                    averageRating: 0,
                    ratingCount: {
                        1: { value: 0, percentage: 0 },
                        2: { value: 0, percentage: 0 },
                        3: { value: 0, percentage: 0 },
                        4: { value: 0, percentage: 0 },
                        5: { value: 0, percentage: 0 },
                    },
                    reviewCount: 0,
                },
            };
        }

        // Helper function to check if a user is a trusted reviewer
        const checkTrustedReviewer = async (customerid) => {
            const reviewCount = await CrmRatings.count({
                where: {
                    customerid,
                    rstatus: 1,
                },
            });
            return reviewCount >= 3;
        };

        // Get unique customer IDs from reviews for trusted reviewer check
        const customerIds = reviews.map((review) => review.customerid);
        if (userReview) {
            customerIds.push(userReview.customerid);
        }
        const uniqueCustomerIds = [...new Set(customerIds)];

        // Check trusted status for all unique customers
        const trustedReviewersMap = {};
        for (const customerId of uniqueCustomerIds) {
            trustedReviewersMap[customerId] =
                await checkTrustedReviewer(customerId);
        }

        const plainReviews = reviews.map((review) => {
            const plain = review.get({ plain: true });
            plain.likes = safeParseArray(plain.likes);
            plain.dislikes = safeParseArray(plain.dislikes);
            // Add trusted reviewer status
            plain.isTrustedReviewer =
                trustedReviewersMap[plain.customerid] || false;
            return plain;
        });

        let plainUserReview = null;
        if (userReview) {
            plainUserReview = userReview.get({ plain: true });
            plainUserReview.likes = safeParseArray(plainUserReview.likes);
            plainUserReview.dislikes = safeParseArray(plainUserReview.dislikes);
            // Add trusted reviewer status for user's own review
            plainUserReview.isTrustedReviewer =
                trustedReviewersMap[plainUserReview.customerid] || false;
        }

        // Search relevance sorting
        if (search) {
            plainReviews.sort((a, b) => {
                const nameA = a.user?.first_name?.toLowerCase() || "";
                const nameB = b.user?.first_name?.toLowerCase() || "";
                const titleA = a.review_title?.toLowerCase() || "";
                const titleB = b.review_title?.toLowerCase() || "";
                const searchLower = search.toLowerCase();

                const nameMatchA = nameA.includes(searchLower) ? 1 : 0;
                const nameMatchB = nameB.includes(searchLower) ? 1 : 0;
                const titleMatchA = titleA.includes(searchLower) ? 1 : 0;
                const titleMatchB = titleB.includes(searchLower) ? 1 : 0;

                // If multi-star filter is applied, prioritize rating first
                if (selectedStarArray && selectedStarArray.length > 1) {
                    if (a.rating !== b.rating) {
                        return b.rating - a.rating;
                    }
                }

                if (nameMatchA !== nameMatchB) return nameMatchB - nameMatchA;
                if (titleMatchA !== titleMatchB)
                    return titleMatchB - titleMatchA;

                return 0;
            });
        }

        // Calculate overall stats (unfiltered data)
        const rawRatingCount = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
        let totalRatingCountForStats = 0;
        let sumAllRatings = 0;

        for (const ratingEntry of totalProductsRating) {
            const ratingVal = parseFloat(ratingEntry.rating);
            if (!isNaN(ratingVal)) {
                totalRatingCountForStats++;
                sumAllRatings += ratingVal;
                const rounded = Math.round(ratingVal);
                if (rounded >= 1 && rounded <= 5) {
                    rawRatingCount[rounded]++;
                }
            }
        }

        const ratingCount = {};
        for (let i = 1; i <= 5; i++) {
            const count = rawRatingCount[i];
            const percentage =
                totalRatingCountForStats > 0
                    ? Number(
                          ((count / totalRatingCountForStats) * 100).toFixed(2)
                      )
                    : 0;
            ratingCount[i] = { value: count, percentage };
        }

        const averageRatingAll =
            totalRatingCountForStats > 0
                ? sumAllRatings / totalRatingCountForStats
                : 0;

        const totalReviewCount = totalProductsRating.filter(
            (i) => i.review_title && i.review_title.trim() !== ""
        ).length;

        return {
            statusCode: 200,
            message: "Reviews fetched successfully",
            status: "success",
            result: plainReviews,
            userReview: plainUserReview,
            stats: {
                totalratingsForPagination: totalFilteredReviews,
                currentPageForPagination: page,
                totalPages: Math.ceil(totalFilteredReviews / limit),
                totalRatingCount: totalRatingCountForStats,
                averageRating: Number(averageRatingAll.toFixed(2)),
                ratingCount,
                reviewCount: totalReviewCount,
            },
        };
    } catch (err) {
        return {
            statusCode: 200,
            message: err.message || "Failed to fetch reviews.",
            status: "failure",
            result: [],
        };
    }
};

// *************** Authorization required ***************
// Create a review
export const addProductReview = async (req) => {
    try {
        const {
            country_id,
            vendor_id,
            orderno,
            productid,
            rating,
            review,
            review_title,
        } = req.body;

        const isAllowedForEveryone = process.env.isAllowedForEveryone == "true";

        if (!isAllowedForEveryone && (!orderno || orderno === "0")) {
            return {
                statusCode: 200,
                message: "Orderno. is required of the product",
                status: "failure",
                result: {},
            };
        }

        const customerid = req.user?.user_id;
        if (!customerid) {
            return {
                statusCode: 200,
                message: `Only logged in users can leave a review.`,
                status: "failure",
                result: {},
            };
        }

        // Validate required fields
        const missingFields = [];
        if (!productid) missingFields.push("productid");
        if (!rating) missingFields.push("rating");

        if (missingFields.length > 0) {
            return {
                statusCode: 200,
                message: `Missing required field(s): ${missingFields.join(", ")}`,
                status: "failure",
                result: {},
            };
        }

        // Validate rating is a number between 1 and 5
        const numericRating = parseFloat(rating);
        if (isNaN(numericRating) || numericRating < 1 || numericRating > 5) {
            return {
                statusCode: 200,
                message: "Rating must be a number between 1 and 5",
                status: "failure",
                result: {},
            };
        }

        // Validate user existence
        const isUser = await CrmCustomer.findOne({
            where: { id: customerid },
        });

        if (!isUser) {
            return {
                statusCode: 200,
                message: "User must be logged in before proceeding.",
                status: "failure",
                result: {},
            };
        }

        // ❗Check if user already reviewed this product in this order
        const existingReview = await CrmRatings.findOne({
            where: {
                productid,
                customerid,
                orderno: orderno || null,
            },
        });

        if (existingReview && ![2, 3].includes(existingReview.rstatus)) {
            return {
                statusCode: 200,
                message: "You’ve already submitted a review for this product",
                status: "failure",
                result: {},
            };
        }

        // Prepare review data
        const reviewData = {
            counrty_id: country_id || 1,
            vendor_id: vendor_id || 0,
            orderno: orderno || null,
            productid,
            customerid,
            rating: numericRating,
            review,
            review_title,
            image: req.filePaths,
            rating_date: new Date(),
            rstatus: 1,
        };

        const created = await CrmRatings.create(reviewData);

        // Update Elasticsearch with new rating statistics
        try {
            await handleProductRatingUpdate(req.server, productid);
        } catch (elasticError) {
            console.error(
                "Failed to update Elasticsearch after rating creation:",
                elasticError
            );
            // Don't fail the request if Elasticsearch update fails
        }

        return {
            statusCode: 201,
            message: "Review Submitted Successfully",
            status: "success",
            result: { ratings_id: created.ratings_id },
        };
    } catch (err) {
        console.log(err);
        return {
            statusCode: 200,
            message: err.message || "Failed to submit review.",
            status: "failure",
            result: {},
        };
    }
};

// Edit a review
export const editProductReview = async (req) => {
    try {
        const { rating, review, review_title, ratings_id, productid, images } =
            req.body;

        const customerid = req.user?.user_id;

        const reviewInstance = await CrmRatings.findOne({
            where: { ratings_id, productid },
        });

        if (!reviewInstance) {
            return {
                statusCode: 200,
                message: "Review not found",
                status: "failure",
                result: {},
            };
        }

        if (reviewInstance.customerid !== customerid) {
            return {
                statusCode: 200,
                message: "Unauthorized access",
                status: "failure",
                result: {},
            };
        }

        if (reviewInstance.isEdited) {
            return {
                statusCode: 200,
                message: "You cannot edit this review again.",
                status: "failure",
                result: {},
            };
        }

        const missingFields = [];
        if (!ratings_id) missingFields.push("ratings_id");
        if (!productid) missingFields.push("productid");
        if (!rating) missingFields.push("rating");
        if (!customerid) missingFields.push("customerid");

        if (missingFields.length > 0) {
            return {
                statusCode: 200,
                message: `Missing required field(s): ${missingFields.join(", ")}`,
                status: "failure",
                result: {},
            };
        }

        const numericRating = parseFloat(rating);
        if (isNaN(numericRating) || numericRating < 1 || numericRating > 5) {
            return {
                statusCode: 200,
                message: "Rating must be a number between 1 and 5",
                status: "failure",
                result: {},
            };
        }
        let newArr;
        const safeImages = Array.isArray(images) ? images : [];

        if (!Array.isArray(req.filePaths)) {
            newArr = [...safeImages];
        } else {
            newArr = [...req.filePaths, ...safeImages];
        }

        if (newArr.length > 5) {
            return {
                statusCode: 200,
                message: "Only 5 images are allowed",
                status: "failure",
                result: {},
            };
        }

        await reviewInstance.update({
            rating: numericRating,
            review,
            review_title,
            image: newArr.length ? newArr : reviewInstance.old_images,
            updated_on: new Date(),
            rstatus: 0,
            isEdited: true,
        });

        // Update Elasticsearch with new rating statistics
        try {
            await handleProductRatingUpdate(req.server, productid);
        } catch (elasticError) {
            console.error(
                "Failed to update Elasticsearch after rating edit:",
                elasticError
            );
            // Don't fail the request if Elasticsearch update fails
        }

        return {
            statusCode: 200,
            message: "Review has been updated.",
            status: "success",
            result: {},
        };
    } catch (err) {
        return {
            statusCode: 200,
            message: err.message || "Failed to update review.",
            status: "failure",
            result: {},
        };
    }
};

// Delete a review
export const deleteProductReview = async (req) => {
    try {
        const { ratings_id, productid } = req.body;
        const customerid = req.user?.user_id;

        const missingFields = [];
        if (!ratings_id) missingFields.push("ratings_id");
        if (!productid) missingFields.push("productid");
        if (!customerid) missingFields.push("customerid");

        if (missingFields.length > 0) {
            return {
                statusCode: 200,
                message: `Missing required field(s): ${missingFields.join(", ")}`,
                status: "failure",
                result: {},
            };
        }

        const reviewInstance = await CrmRatings.findOne({
            where: { ratings_id, productid },
        });

        if (!reviewInstance) {
            return {
                statusCode: 200,
                message: "Review not found",
                status: "failure",
                result: {},
            };
        }

        if (reviewInstance.customerid !== customerid) {
            return {
                statusCode: 200,
                message: "Unauthorized access",
                status: "failure",
                result: {},
            };
        }

        reviewInstance.rstatus = 3;
        await reviewInstance.save();

        // Update Elasticsearch with new rating statistics after deletion
        try {
            await handleProductRatingUpdate(req.server, productid);
        } catch (elasticError) {
            console.error(
                "Failed to update Elasticsearch after rating deletion:",
                elasticError
            );
            // Don't fail the request if Elasticsearch update fails
        }

        return {
            statusCode: 200,
            message: "Review deleted successfully.",
            status: "success",
            result: {},
        };
    } catch (err) {
        return {
            statusCode: 200,
            message: err.message || "Failed to delete review.",
            status: "failure",
            result: {},
        };
    }
};

// Like a Review
export const toggleLikeOnReview = async (req) => {
    try {
        const { review_id, productid } = req.body;
        const user_id = req.user?.user_id;

        if (!user_id) {
            return {
                statusCode: 200,
                message: `Only logged in users can vote.`,
                status: "failure",
                result: {},
            };
        }

        if (!review_id || !productid) {
            return {
                statusCode: 200,
                message: "review_id, and productid are required",
                status: "failure",
                result: {},
            };
        }
        const review = await CrmRatings.findOne({
            where: { ratings_id: review_id, productid },
        });

        if (!review) {
            return {
                statusCode: 202,
                message: "Review not found",
                status: "failure",
                result: {},
            };
        }

        let likes = [];
        let dislikes = [];

        try {
            likes = JSON.parse(review.likes || "[]");
            if (!Array.isArray(likes)) likes = [];
        } catch {
            likes = [];
        }

        try {
            dislikes = JSON.parse(review.dislikes || "[]");
            if (!Array.isArray(dislikes)) dislikes = [];
        } catch {
            dislikes = [];
        }

        // Remove from dislikes if present
        if (dislikes.includes(user_id)) {
            dislikes = dislikes.filter((id) => id !== user_id);
        }

        let action;
        if (likes.includes(user_id)) {
            // If already liked, remove like
            likes = likes.filter((id) => id !== user_id);
            action = "removed";
        } else {
            // Add like
            likes.push(user_id);
            action = "added";
        }

        // Save changes
        review.likes = JSON.stringify(likes);
        review.dislikes = JSON.stringify(dislikes);
        await review.save();

        return {
            statusCode: 200,
            message: `Vote ${action} successfully`,
            status: "success",
            result: {
                review_id,
                totalLikes: likes.length,
                totalDislikes: dislikes.length,
            },
        };
    } catch (err) {
        return {
            statusCode: 200,
            message: err.message || "Something went wrong",
            status: "failure",
            result: {},
        };
    }
};

// Dislike a review
export const toggleDislikeOnReview = async (req) => {
    try {
        const { review_id, productid } = req.body;
        const user_id = req.user?.user_id;

        if (!user_id) {
            return {
                statusCode: 200,
                message: "Only logged in users can vote.",
                status: "failure",
                result: {},
            };
        }

        if (!review_id || !productid) {
            return {
                statusCode: 200,
                message: "review_id and productid are required",
                status: "failure",
                result: {},
            };
        }

        const review = await CrmRatings.findOne({
            where: { ratings_id: review_id, productid },
        });

        if (!review) {
            return {
                statusCode: 200,
                message: "Review not found",
                status: "failure",
                result: {},
            };
        }

        let likes = [];
        let dislikes = [];

        try {
            likes = JSON.parse(review.likes || "[]");
            if (!Array.isArray(likes)) likes = [];
        } catch {
            likes = [];
        }

        try {
            dislikes = JSON.parse(review.dislikes || "[]");
            if (!Array.isArray(dislikes)) dislikes = [];
        } catch {
            dislikes = [];
        }

        // Remove from likes if present
        if (likes.includes(user_id)) {
            likes = likes.filter((id) => id !== user_id);
        }

        let action;
        if (dislikes.includes(user_id)) {
            // If already disliked, remove
            dislikes = dislikes.filter((id) => id !== user_id);
            action = "removed";
        } else {
            // Add dislike
            dislikes.push(user_id);
            action = "added";
        }

        // Save updated values
        review.likes = JSON.stringify(likes);
        review.dislikes = JSON.stringify(dislikes);
        await review.save();

        return {
            statusCode: 200,
            message: `Dislike ${action} successfully`,
            status: "success",
            result: {
                review_id,
                totalLikes: likes.length,
                totalDislikes: dislikes.length,
            },
        };
    } catch (err) {
        return {
            statusCode: 200,
            message: err.message || "Something went wrong",
            status: "failure",
            result: {},
        };
    }
};

// Get all reveiws of a user
export const getUserReviews = async (req) => {
    try {
        const customerid = req.user?.user_id;

        if (!customerid) {
            return {
                statusCode: 200,
                message: "Only logged in users can access their reviews.",
                status: "failure",
                result: [],
            };
        }

        const reviews = await CrmRatings.findAll({
            where: { customerid, rstatus: 1 },
            order: [["rating_date", "DESC"]],
        });

        if (!reviews || reviews.length === 0) {
            return {
                statusCode: 200,
                message: "No reviews found for this user.",
                status: "failure",
                result: [],
            };
        }

        return {
            statusCode: 200,
            message: "User reviews fetched successfully.",
            status: "success",
            result: reviews,
        };
    } catch (err) {
        return {
            statusCode: 200,
            message: err.message || "Failed to fetch user reviews.",
            status: "failure",
            result: [],
        };
    }
};
