import {
    subcategoryItemsElk,
    callCommonElk,
    bundleClearanceSaleELK,
    brandWeekElk,
    topPicksElk,
    getDealOfTheDayAndSaverZoneElk,
    brandWeekElkChange,
    categoryItemsOneELK,
} from "../elk/home.elk.js";

import { getProductsBySectionElk } from "../elk/common.elk.js";

import { getCmsCustomData, transformProductItem } from "./common.service.js";
import {
    CURRENCY,
    getHomeTopSelling,
    DYNAMIC_BANNERS_KEYS,
} from "../region/config.js";
import { currentDateTime } from "../utils/dateTimeHandler.js";

import { Op, fn, col, literal } from "sequelize";
import CatalogProduct from "../models/catalog_product.model.js";
import CatalogProductInventory from "../models/catalog_product_inventory.model.js";
import CatalogCategory from "../models/catalog_category.model.js";
import CmsBanners from "../models/cms_banners.model.js";
import CatalogBrand from "../models/catalog_brand.model.js";
import AdminLists from "../models/admin_list.model.js";

export const categoryItemsService = async (request) => {
    try {
        const { country_id } = request;
        const currency = CURRENCY[country_id].currency;

        const filtered = await CatalogProduct.findAll({
            attributes: [
                [fn("COUNT", col("CatalogProduct.categoryid")), "total"],
                [col("subcategory.categoryid"), "categoryid"],
                [col("subcategory.category_name"), "subcategory_name"],
                [col("subcategory.slug"), "url"],
            ],
            include: [
                {
                    model: CatalogProductInventory,
                    as: "inventory",
                    attributes: [], // not needed in aggregation
                    where: {
                        stock_status: 1,
                        rstatus: 1,
                        selling_price: { [Op.gt]: 0 },
                        inventory: { [Op.gt]: 0 },
                        country_id,
                    },
                    required: true,
                },
                {
                    model: CatalogCategory,
                    as: "subcategory",
                    attributes: [],
                    where: {
                        front_view: 1,
                    },
                },
            ],
            where: {
                //featured_flag: 1, //front_view change featured_flag
                rstatus: 1,
            },
            group: ["CatalogProduct.categoryid"],
            having: literal("COUNT(`CatalogProduct`.`categoryid`) > 5"),
            order: [[col("subcategory.position"), "ASC"]],
            raw: true,
        });

        const results = await Promise.all(
            filtered.map(async (subcategory) => {
                const req = { query: { subcat_url: subcategory.url, page: 1 } };
                const esresult = await subcategoryItemsElk(
                    req,
                    request.server,
                    country_id
                );

                const products = esresult.map((item) => {
                    return {
                        id: item.id,
                        brand_id: item.brand_id,
                        subcategory_id: item.subcategory_id,
                        ...transformProductItem(item, currency),
                    };
                });

                return {
                    ...subcategory,
                    items: products,
                };
            })
        );

        return {
            status: "success",
            statusCode: 200,
            message: "Ourshopee Category items fetched successfully",
            data: results,
        };
    } catch (err) {
        throw err;
    }
};

export const categoryItemsOneService = async (request) => {
    try {
        const results = await categoryItemsOneELK(request);

        return {
            status: "success",
            statusCode: 200,
            message: "Ourshopee Category items fetched successfully",
            data: results,
        };
    } catch (err) {
        throw err;
    }
};

export const getbannerService = async (country_id) => {
    try {
        const result = await CmsBanners.findAll({
            attributes: ["id", "banner_images", "type"],
            where: {
                country_id,
                rstatus: 1,
                type: 4,
                position: {
                    [Op.between]: [1, 4],
                },
            },
            order: [["position", "ASC"]],
            raw: true,
            // logging: console.log,
        });

        const output = result.map((res) => {
            const data = getCmsCustomData(res.banner_images, res.type);
            // console.log("Banner Data:", data);
            return {
                banner_id: res.id,
                banner_image: data.image ? data.image : "",
                url: data.url ? data.url : "",
            };
        });

        return output;
    } catch (err) {
        throw err;
    }
};

export const getCarouselListService = async (country_id) => {
    try {
        // const imagePath = process.env.CAROUSEL_IMG_PATH;
        // OurshopeeImageSlider //"id", "url", "image", "mobile_image"
        const result = await CmsBanners.findAll({
            attributes: ["id", "banner_images"],
            where: {
                rstatus: 1,
                country_id,
                type: 1,
            },
            order: [["position", "ASC"]],
            limit: 10,
            raw: true,
            // logging: console.log,
        });

        const output = result.map((item) => {
            // const domain = new URL(item.url);
            const data = getCmsCustomData(item.banner_images, item.type);

            return {
                carousel_id: item.id,
                image_url: data.image ? data.image : "",
                mobile_image_url: data.mobile_image ? data.mobile_image : "",
                url: data.url ? data.url : "",
            };
        });

        return output;
    } catch (err) {
        throw err;
    }
};

export const getDynamicBanners = async (country_id) => {
    try {
        const banners = {};
        const getTypeId = await AdminLists.findOne({
            attributes: ["list_value"],
            where: {
                country_id,
                list_key: "home_dynamic_banners",
            },
        });

        if (!getTypeId) return {};

        const getBanners = await CmsBanners.findAll({
            attributes: ["position", "banner_images"],
            where: { type_id: getTypeId.dataValues.list_value },
        });
        const baseURL = process.env.CDN_IMAGE_BASE_URL;
        getBanners.forEach((banner) => {
            const images = JSON.parse(banner.dataValues.banner_images);
            banners[DYNAMIC_BANNERS_KEYS[banner.dataValues.position]] = {
                position: banner.dataValues.position,
                url_web: images.images[0]?.inputData.url,
                url_app: images.mobile_images[0]?.inputData.url,
                image_web: baseURL + images.images[0]?.original,
                image_app: baseURL + images.mobile_images[0]?.original,
            };
        });
        // console.log(banners)
        return banners;
    } catch (err) {
        console.log("Error from getDynamicBanner", err);
        return "error";
    }
};

export const multibannersListService = async (country_id) => {
    try {
        // Step 1: Get category with position = 2
        // OurshopeeHomeCategory
        // Multi banner data is not comming need to check logic with shrikant
        /*const category = await AdminLists.findOne({
            attributes: ["list_value"],
            where: {
                list_value: 22,
                // display_order: 3,
                country_id,
            },
            raw: true,
        });

        if (!category) {
            return []; 
        }*/

        // Step 2: Fetch related banners
        // OurshopeeHomeBanner
        const banners = await CmsBanners.findAll({
            where: {
                type: 2,
                type_id: 1,
                // country_id :2
            },
            order: [["position", "ASC"]],
            limit: 2,
        });

        const output = banners.map((el) => {
            // const domain = new URL(el.mobile_url);
            const data = getCmsCustomData(el.banner_images, el.type);
            // console.log("Banner Data:", data);
            return {
                banner_id: el.id,
                banner_image: data.image ? data.image : "",
                url: data.url ? data.url : "",
            };
        });

        return output;
    } catch (err) {
        throw err;
    }
};

export const getExcitingOffersService = async (countryId, fastify) => {
    try {
        const currency = CURRENCY[countryId].currency;
        const homeDealOffersData = await callCommonElk(countryId, fastify);

        const excitingOffers = (homeDealOffersData.exciting_offers || []).map(
            (item) => transformProductItem(item, currency)
        );
        const dealOfTheDay = (homeDealOffersData.deal_of_the_day || []).map(
            (item) => transformProductItem(item, currency)
        );

        return {
            exciting_offers: excitingOffers,
            deal_of_the_day: dealOfTheDay,
        };
    } catch (err) {
        throw err;
    }
};

export const bundleClearanceSaleService = async (countryId, fastify) => {
    try {
        const currency = CURRENCY[countryId].currency;
        const result = await bundleClearanceSaleELK(countryId, fastify);

        const clearanceSale = (result.clearance_sale || []).map((item) =>
            transformProductItem(item, currency)
        );

        const bundleDeals = (result.bundle_deals || []).map((item) =>
            transformProductItem(item, currency)
        );

        return {
            clearance_sale: clearanceSale,
            bundle_deals: bundleDeals,
        };
    } catch (err) {
        throw err;
    }
};

export const brandWeekService = async (req) => {
    try {
        const { country_id } = req;
        const currency = CURRENCY[country_id].currency;

        // Fetch sliders with brand data
        // OurshopeeAllSlider
        // SELECT s.*,b.url as slug FROM ourshopee_all_sliders s
        // left join ourshopee_brands b on b.id = s.brand_id
        // WHERE slider_type = 'week_brand' and status = 1 and country_id = '${country_id}' and status = 1

        const sliders = await CmsBanners.findOne({
            attributes: ["banner_images", "type_id", "type", "brandid"],
            where: {
                type: 3,
                type_id: 2,
                country_id,
            },
            include: [
                {
                    model: CatalogBrand,
                    as: "brand",
                    attributes: ["brandid"],
                    required: true, // performs an INNER JOIN
                },
            ],
            raw: true,
        });

        if (!sliders || sliders.length === 0) return [];

        // console.log('sliders', sliders[0]?.['brand.brandid'])
        const firstBrandId = sliders?.["brand.brandid"] || "";

        // countryId, brand_id, fastify
        const brandProducts = await brandWeekElk(
            country_id,
            firstBrandId,
            req.server
        );

        // const output = sliders.map((slider) => {
        const data = getCmsCustomData(sliders.banner_images, sliders.type);

        return [
            {
                desktopImage: data.image ? data.image : "",
                mobileImage: data.mobile_image ? data.mobile_image : "",
                sku: sliders.brand?.slug || "",

                items: brandProducts.map((item) =>
                    transformProductItem(item, currency)
                ),
            },
        ];
        // });

        return output;
    } catch (err) {
        throw err;
    }
};

export const getTopPicksService = async (req) => {
    try {
        const { country_id } = req;

        const currency = CURRENCY[country_id].currency;
        // Fetch sliders + brand info using Sequelize
        // OurshopeeAllSlider

        const sliders = await CmsBanners.findAll({
            attributes: ["brandid", "banner_images", "type_id", "type"],
            where: {
                type: 3,
                type_id: 1,
                country_id,
            },
            include: [
                {
                    model: CatalogBrand,
                    as: "brand",
                    attributes: ["brandid", "slug"],
                    required: true, // performs an INNER JOIN
                },
            ],
            raw: true,
        });

        if (!sliders || sliders.length === 0) return [];

        const brandIds = sliders.map((slider) => slider.brandid);

        const elk_output = await topPicksElk(req, brandIds);

        // Format response
        const result = sliders.map((slider) => {
            const data = getCmsCustomData(slider.banner_images, slider.type);

            const brandId = slider.brandid;
            const brandUrl = slider?.["brand.slug"] || "";

            // Match brand bucket from ELK response
            const matchedElk = elk_output.find((b) => b.key == brandId);
            const hits = matchedElk?.docs?.hits?.hits || [];

            const productlist = hits.map((item) => {
                const product = item._source || {};
                const inventory =
                    item.inner_hits?.inventory_hit?.hits?.hits?.[0]?._source ||
                    {};

                const combined = {
                    ...product,
                    ...inventory,
                };

                const transformed = transformProductItem(combined, currency);
                return transformed;
            });

            return {
                brand_id: brandId,
                image_slider: data.image ? data.image : "",
                url: brandUrl,
                productlist,
            };
        });

        return result;
    } catch (err) {
        throw err;
    }
};

export const getTopSellingService = async (req) => {
    try {
        const { country_id } = req;
        const currency = CURRENCY[country_id].currency;

        var input_data = getHomeTopSelling[country_id];

        if (!input_data) {
            return [];
        }

        let topItemsList = await getProductsBySectionElk(req, input_data);

        // Remove duplicates by ID
        const seenIds = new Set();
        const uniqueItems = topItemsList.filter((item) => {
            if (seenIds.has(item.id)) return false;
            seenIds.add(item.id);
            return true;
        });

        const finalList = Array.from(uniqueItems).map((item) =>
            transformProductItem(item, currency)
        );

        return finalList.length > 0 ? finalList : [];
    } catch (err) {
        throw err;
    }
};

export const getSaverZoneService = async (req) => {
    try {
        const { country_id } = req;
        const currency = CURRENCY[country_id].currency;
        const currentDate = currentDateTime();
        // input data same for all country
        const input_data = {
            section_id: 16,
            offset: 0,
            limit: 4,
            status: 1,
            quantity: 0,
            stock: "In stock",
            special_price: 0,
            offer_from: currentDate,
            order_by: "offer_from",
            type: "saver",
        };

        const elkResults = await getDealOfTheDayAndSaverZoneElk(
            input_data,
            req
        );

        // Remove duplicates by id using Set
        const seenIds = new Set();
        const uniqueItems = elkResults.filter((item) => {
            if (seenIds.has(item.id)) return false;
            seenIds.add(item.id);
            return true;
        });

        const topItemsList = Array.from(uniqueItems).map((item) =>
            transformProductItem(item, currency)
        );

        return topItemsList.length > 0 ? topItemsList : [];
    } catch (err) {
        throw err;
    }
};

export const getDealOfTheDayService = async (req) => {
    try {
        const currentDate = currentDateTime();
        const currency = CURRENCY[req.country_id].currency;
        // inputData same for all country
        const inputData = {
            section_id: 29,
            offset: 0,
            limit: 4,
            from_date: currentDate,
        };

        const rawItems = await getDealOfTheDayAndSaverZoneElk(inputData, req);

        // console.log("rawItems", rawItems);
        /* const uniqueItems = rawItems.filter(
            (item, index, self) =>
                index === self.findIndex((t) => t.id === item.id)
        ); */
        const seen = new Set();
        const uniqueItems = rawItems.filter((item) => {
            if (seen.has(item.id)) return false;
            seen.add(item.id);
            return true;
        });

        // console.log('uniqueItems', uniqueItems)
        const result = Array.from(uniqueItems).map((item) =>
            transformProductItem(item, currency)
        );

        return result.length > 0 ? result : [];
    } catch (err) {
        throw err;
    }
};

export const getELKDataService = async (req) => {
    const country_id = req.country_id;
    const currency = CURRENCY[req.country_id].currency;

    const brandProducts = await brandWeekElkChange(country_id, 7, req.server);

    // console.log("brandProducts", brandProducts);
    const items = brandProducts.map((item) =>
        transformProductItem(item, currency)
    );
    return items;
};
