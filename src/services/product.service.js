import OmsPayfortBankdetails from "../models/oms_payfort_bankdetails.model.js";
import CatalogProduct from "../models/catalog_product.model.js";
import CatalogProductInventory from "../models/catalog_product_inventory.model.js";
import CatalogCategory from "../models/catalog_category.model.js";
import CatalogVariant from "../models/catalog_variant.model.js";
import CatalogAttributeValue from "../models/catalog_attribute_value.model.js";
import { currentDateTime } from "../utils/dateTimeHandler.js";
import {
    searchRelatedProducts,
    searchRecentlyViewed,
    availableAttributesElk,
} from "../elk/product.elk.js";
import { priceCalculator } from "./common.service.js";
import { SKU_LIST } from "../region/SkuList.js";
import moment from "moment";
import fastify from "../app.js";
import { Op } from "sequelize";
import { CURRENCY } from "../region/config.js";

const COLOR_ATTRIBUTE_ID = 1;
const STORAGE_ATTRIBUTE_ID = 3;
const CDN_IMAGE_BASE_URL = process.env.CDN_IMAGE_BASE_URL;

const product_bank_offers = async () => {
    try {
        return await OmsPayfortBankdetails.findAll({
            where: { status: 1 },
            raw: true,
        });
    } catch (error) {
        console.error("Error fetching bank offers:", error);
        return [];
    }
};

const tabbyactiveFun = async (subcat_id, display_price) => {
    try {
        const subcategory = await CatalogCategory.findOne({
            where: { categoryid: subcat_id },
            raw: true,
        });
        if (subcategory) {
            let tabbyType = subcategory.tabby_type == 1 ? 1 : 0;
            let tabbyactive = 0;

            if (display_price > 2500) {
                tabbyactive = 0;
            } else if (tabbyType == 1 && display_price > 1500) {
                tabbyactive = 0;
            } else {
                tabbyactive = 1;
            }
            return tabbyactive;
        }
        return 0;
    } catch (error) {
        console.error("Error in tabbyactiveFun:", error);
        return 0;
    }
};

const formatDeliveryTime = async (delay) => {
    const now = currentDateTime();
    const deliveryDate = moment(now).add(delay, "days");
    const day = deliveryDate.format("dddd").substring(0, 3);
    const month = deliveryDate.format("MMMM").substring(0, 3);
    return `Delivery Expected By ${day}, ${month} ${deliveryDate.format("DD")}`;
};

export const DeliveryExpectedBy = async (sku, countryId) => {
    try {
        const skuList = SKU_LIST[countryId];

        // console.log('skuList', skuList.includes(sku))
        if (skuList.includes(sku)) {
            // console.log('skuList.includes(sku)', skuList.includes(sku))
            return {
                fastTrack: 1,
                delivery: "Delivery Expected By Tomorrow",
            };
        }
        const product = await CatalogProduct.findOne({
            where: { sku },
            attributes: ["productid", "sku", "categoryid"],
            include: {
                model: CatalogProductInventory,
                as: "inventory",
                attributes: ["inventory"],
                required: true,
                where: {
                    country_id: countryId, // replace with actual value
                },
            },
            // logging: console.log
        });
        // console.log('product dataata', product)
        let fastTrack = 0;
        let expected_delivery_date = "";

        if (product) {
            const quantity = product.inventory?.inventory || 0;

            if (product.categoryid === 183) {
                expected_delivery_date = "Delivered within : 1 - 2 days";
            } else if (quantity >= 3) {
                // fastTrack = 1;
                expected_delivery_date = await formatDeliveryTime(2);
            } else {
                expected_delivery_date = await formatDeliveryTime(5);
            }
        } else {
            expected_delivery_date = await formatDeliveryTime(5);
        }
        return { fastTrack, delivery: expected_delivery_date };
    } catch (error) {
        console.error("Error in DeliveryExpectedBy:", error);
        return { fastTrack: 0, delivery: await formatDeliveryTime(5) };
    }
};

const getVariantAttributes = async (variantGroupId) => {
    try {
        const variants = await CatalogVariant.findAll({
            where: { variantgroupid: variantGroupId },
            attributes: ["variant_code", "attributevalues_ids"],
            raw: true,
        });

        if (!variants.length) {
            return [];
        }
        const allAttributeIds = new Set();
        variants.forEach((variant) => {
            variant.attributevalues_ids.split(",").forEach((id) => {
                if (id) allAttributeIds.add(id);
            });
        });

        if (allAttributeIds.size === 0) {
            return [];
        }

        const attributeDetails = await CatalogAttributeValue.findAll({
            where: {
                attributevalueid: {
                    [Op.in]: [...allAttributeIds], // e.g., WHERE attributevalueid IN (8, 209, 128, 30)
                },
            },
            attributes: [
                "attributevalueid",
                "name",
                "slug",
                "attributeid",
                "color_code",
            ],
            raw: true,
        });

        const attributeMap = new Map(
            attributeDetails.map((attr) => [attr.attributevalueid, attr])
        );

        const finalResults = [];
        variants.forEach((variant) => {
            variant.attributevalues_ids.split(",").forEach((idStr) => {
                const id = parseInt(idStr, 10);
                const attribute = attributeMap.get(id);

                if (attribute) {
                    finalResults.push({
                        variant_code: variant.variant_code,
                        attributevalueid: attribute.attributevalueid,
                        name: attribute.name,
                        // code: attribute.slug, // Aliasing 'slug' to 'code'
                        code: attribute.color_code,
                        attributeid: attribute.attributeid,
                    });
                }
            });
        });

        return finalResults;
    } catch (err) {
        console.error("Error fetching variant attributes with Sequelize:", err);
        return [];
    }
};

export const productDetailsService = async (request) => {
    try {
        const { sku } = request.query;
        const client = fastify.elasticsearch;
        const countryId = request.country_id;
        const currency = CURRENCY[request.country_id].currency;

        const result = await client.search({
            index: process.env.ELASTIC_PRODUCT_INDEX,
            body: {
                _source: [
                    "id",
                    "sku",
                    "name",
                    "url",
                    "image",
                    "details",
                    "video_link",
                    "brand_id",
                    "brand_name",
                    "category_id",
                    "category_name",
                    "category_url",
                    "subcategory_id",
                    "subcategory_name",
                    "subcategory_url",
                    "sub_sub_category_id",
                    "sub_sub_category_name",
                    "sub_sub_category_url",
                    "color_id",
                    "color_name",
                    "storage_id",
                    "size_id",
                    "brands_category_id",
                    "variantgroupid",
                    "variant_flag",
                    "from_date",
                    "to_date",
                    "offer_from",
                    "offer_to",
                    "warranty",
                    "warranty_type",
                    "attributes",
                    "product_images",
                    "OS",
                    "Storage",
                    "avg_rating",
                    "total_ratings",
                    "reviews",
                ],
                query: {
                    bool: {
                        must: [
                            { match: { sku: sku } },
                            {
                                nested: {
                                    path: "inventory",
                                    query: {
                                        bool: {
                                            must: [
                                                {
                                                    term: {
                                                        "inventory.country_id":
                                                            countryId,
                                                    },
                                                },
                                                {
                                                    term: {
                                                        "inventory.rstatus": 1,
                                                    },
                                                },
                                                {
                                                    match_phrase: {
                                                        "inventory.stock":
                                                            "In stock",
                                                    },
                                                },
                                            ],
                                        },
                                    },
                                    inner_hits: {
                                        name: "inventory_hit",
                                        size: 1,
                                        _source: [
                                            "inventory.price",
                                            "inventory.selling_price",
                                            "inventory.special_price",
                                            "inventory.promotion_price",
                                            "inventory.from_date",
                                            "inventory.to_date",
                                            "inventory.stock",
                                            "inventory.quantity",
                                            "inventory.country_id",
                                        ],
                                        sort: [
                                            {
                                                "inventory.price": {
                                                    order: "asc",
                                                },
                                            },
                                        ],
                                    },
                                },
                            },
                        ],
                    },
                },
            },
        });

        if (!result.hits.hits.length) {
            return {
                statusCode: 404,
                message: "Product not found!",
                status: "failure",
                result: {},
            };
        }
        const hit = result.hits.hits[0];
        const values = hit._source;
        console.log("values :", values);
        const inventoryData = hit.inner_hits.inventory_hit.hits.hits[0]._source;

        const priceOutput = priceCalculator(inventoryData);
        const display_price = priceOutput.display_price;
        const percentage = priceOutput.percentage;

        const now = currentDateTime();

        const bank_offers_output =
            display_price > 500 ? await product_bank_offers() : [];

        const bank_offers = bank_offers_output.map((element) => {
            const tenures = [3, 6, 9, 12, 18, 24, 36];
            const plans = [];
            let lastValidPayAmount = 0;

            const calculateEMI = (tenure) => {
                const interestRate = element[`${tenure}-month-intrest`];
                if (!interestRate) {
                    return null;
                }

                const processingFeeValue =
                    element[`${tenure}-month-processing`] || 0;
                const processingFee =
                    element["processing_fee_type"] === ""
                        ? (display_price * parseInt(processingFeeValue)) / 100
                        : parseInt(processingFeeValue);

                const totalAmount = display_price + processingFee;
                const emiInterest =
                    (totalAmount * parseFloat(interestRate)) / 100;
                const emiAmount = totalAmount / tenure;
                const payAmount = emiAmount + emiInterest;
                lastValidPayAmount = payAmount;

                return {
                    time_period: `${tenure} Months`,
                    description: {
                        interest: `${tenure} EMI's @ ${interestRate}%`,
                        payamount: `${currency} ${payAmount.toFixed(2)}/Month`,
                        price: `${currency} ${display_price}`,
                        interest_num: `${interestRate}%`,
                        processing_fee: `${currency} ${processingFee.toFixed(2)}`,
                        emi_interest_to_bank: `${currency} ${(emiInterest * tenure).toFixed(2)}`,
                        total_amnt_left: `${tenure}x${payAmount.toFixed(2)}`,
                        total_amnt_right: `${currency} ${(payAmount * tenure).toFixed(2)}`,
                    },
                };
            };

            tenures.forEach((tenure) => {
                const plan = calculateEMI(tenure);
                plans.push(
                    plan
                        ? plan
                        : { time_period: `${tenure} Months`, description: [] }
                );
            });

            return {
                id: element.id,
                value: `Standard EMI Plans. Pay ${currency} ${lastValidPayAmount.toFixed(2)} with`,
                image: `${CDN_IMAGE_BASE_URL}/images/payfort-bank/${element.logo}`,
                image_png: `${CDN_IMAGE_BASE_URL}/images/payfort-bank/${element.logo_png}`,
                tag: element.bank_name,
                modal_data: {
                    bank_name: element.bank_name,
                    bank_id: element.id,
                    plans: plans,
                },
            };
        });

        let attArr;

        if (
            values.brands_category_id != null &&
            values.brands_category_id != "" &&
            values.brands_category_id != 0
        ) {
            const allAttributesFromDB = await getVariantAttributes(
                values.brands_category_id
            );

            if (!allAttributesFromDB || allAttributesFromDB.length === 0) {
                attArr = [
                    { id: 1, title: "Available Colors", list: [] },
                    { id: 2, title: "Available Storage", list: [] },
                ];
            } else {
                const colorIds = [];
                const storageIds = [];
                const attributeMap = new Map();

                allAttributesFromDB.forEach((attr) => {
                    attributeMap.set(attr.attributevalueid, attr);
                    if (attr.attributeid === COLOR_ATTRIBUTE_ID) {
                        colorIds.push(attr.attributevalueid);
                    } else if (attr.attributeid === STORAGE_ATTRIBUTE_ID) {
                        storageIds.push(attr.attributevalueid);
                    }
                });

                const uniqueColorIds = [...new Set(colorIds)];
                const uniqueStorageIds = [...new Set(storageIds)];

                const attributesOutputColorsElk = await availableAttributesElk(
                    countryId,
                    values.brands_category_id,
                    uniqueColorIds,
                    "colors",
                    values.storage_id
                );

                const attributesOutputStorageElk = await availableAttributesElk(
                    countryId,
                    values.brands_category_id,
                    uniqueStorageIds,
                    "storage",
                    values.color_id
                );

                attArr = [
                    {
                        id: 1,
                        title: "Available Colors",
                        list: attributesOutputColorsElk
                            .map((ele) => {
                                const dbInfo = attributeMap.get(ele.color_id);
                                return {
                                    id: ele.color_id,
                                    name: dbInfo ? dbInfo.name : "",
                                    url: `${ele.url}/${ele.sku}`,
                                    sku: ele.sku,
                                    code: dbInfo ? dbInfo.code : "",
                                };
                            })
                            .filter((col) => col.id != 0),
                    },
                    {
                        id: 2,
                        title: "Available Storage",
                        list: attributesOutputStorageElk
                            .map((ele) => {
                                const dbInfo = attributeMap.get(ele.storage_id);
                                return {
                                    id: ele.storage_id,
                                    name: dbInfo ? dbInfo.name : "",
                                    url: `${ele.url}/${ele.sku}`,
                                    sku: ele.sku,
                                    code: "",
                                };
                            })
                            .filter((col) => col.id != 0),
                    },
                ];
            }
        } else {
            attArr = [];
        }

        let countdownDate = null;
        if (
            (values.offer_from < now && values.to_date > now) ||
            (values.offer_from < now && values.offer_to > now)
        ) {
            countdownDate =
                values.from_date < now && values.to_date > now
                    ? values.to_date
                    : values.offer_to;
        }
        var tabbyactiveFunOp = await tabbyactiveFun(
            values.subcategory_id,
            display_price
        );
        var DeliveryExpectedByOp = await DeliveryExpectedBy(
            values.sku,
            request.country_id
        );

        const addDescItem = (title, value) => (value ? [{ title, value }] : []);
        const operatingSystemAttr = values.attributes.find(
            (attr) => attr.key === "Operating System"
        );
        const storageAttr = values.attributes.find(
            (attr) => attr.key === "Storage"
        );

        const product = [
            {
                id: values.id,
                brand_id: values.brand_id,
                color_id: values.color_id,
                storage_id: values.storage_id,
                size_id: values.size_id,
                category_id: values.category_id,
                category_name: values.category_name,
                category_url: values.category_url,
                subcategory_id: values.subcategory_id,
                subcategory_name: values.subcategory_name,
                subcategory_url: values.subcategory_url,
                sub_sub_category_id: values.sub_sub_category_id,
                sub_sub_category_name: values.sub_sub_category_name,
                sub_sub_category_url: values.sub_sub_category_url,
                name: values.name,
                display_price: display_price,
                percentage: Math.round(percentage),
                avg_rating: values.avg_rating,
                total_ratings: values.total_ratings,
                reviews: values.reviews,

                ...(inventoryData.country_id !== 3 && {
                    old_price: inventoryData.price,
                }),
                image: `${CDN_IMAGE_BASE_URL}${values.image.replace("/thump", "")}`,
                stock: inventoryData.stock,
                quantity: inventoryData.quantity,
                url: values.url,
                alternateAttributes: attArr,
                sku: values.sku,
                tabbyactive: tabbyactiveFunOp,
                details: values.details,
                ...(((values.from_date < currentDateTime &&
                    values.to_date > currentDateTime) ||
                    (values.offer_from < currentDateTime &&
                        values.offer_to > currentDateTime)) && {
                    countdown: countdownDate,
                }),
                brand: values.brand_name,
                fastTrack: DeliveryExpectedByOp.fastTrack,
                delivery: DeliveryExpectedByOp.delivery,
                images:
                    values.product_images && values.product_images.length > 0
                        ? [
                              //   `${CDN_IMAGE_BASE_URL}${values.image.replace("/thump", "")}`,
                              ...values.product_images.map(
                                  (img) => `${CDN_IMAGE_BASE_URL}${img}`
                              ),
                          ]
                        : [
                              `${CDN_IMAGE_BASE_URL}${values.image.replace("/thump", "")}`,
                          ],
                video_link: values.hasOwnProperty("video_link")
                    ? values.video_link
                    : "",
                small_desc_data: [
                    { title: "SKU", value: values.sku },
                    { title: "Brand", value: values.brand_name },
                    ...addDescItem(
                        "Operating System",
                        operatingSystemAttr?.value
                    ),
                    ...addDescItem("Storage", storageAttr?.value),
                    ...addDescItem("OS", values.OS),
                    ...addDescItem("Storage", values.Storage),
                    ...addDescItem("Color", values.color_name),
                    ...addDescItem(
                        "Product Warranty",
                        values.warranty > 0
                            ? `${values.warranty} ${values.warranty_type === "2" ? "Days" : "Months"}`
                            : null
                    ),
                ].filter((item) => item.value),
                bank_offers: bank_offers,
            },
        ];

        return {
            statusCode: 200,
            message: "Product details fetched successfully!",
            status: "success",
            result: { product },
        };
    } catch (err) {
        console.error("Error in productDetailsService:", err);
        throw err;
    }
};

export const getRelatedItemsService = async (request) => {
    try {
        const { brand_id, subcategory_id, sku, skulist } = request.body;
        const client = fastify.elasticsearch;
        const countryId = request.headers["country-id"];
        const indexName = process.env.ELASTIC_PRODUCT_INDEX;

        const [related_products, recently_viewed] = await Promise.all([
            searchRelatedProducts(
                countryId,
                client,
                indexName,
                brand_id,
                subcategory_id,
                sku
            ),
            searchRecentlyViewed(countryId, client, indexName, skulist),
        ]);

        return {
            statusCode: 200,
            message: "Related and recently viewed items fetched successfully!",
            status: "success",
            result: { related_products, recently_viewed },
        };
    } catch (err) {
        console.error("Error in getRelatedItemsService:", err);
        throw err;
    }
};
