import AdminVendor from "../models/admin_vendor.model.js";
import AdminVendorLocation from "../models/admin_vendor_location.model.js";
import { processMultipartAndUpload } from "../utils/multipartImageUpload.js";
import sequelize from "../db/sequelize.js";
import AdminCountryEmirates from "../models/admin_country_emirates.model.js";
import AdminCountryArea from "../models/admin_country_area.model.js";
import { Op } from "sequelize";
import { sendEmailUsingMailGen } from "../utils/email.js";

const safeRollback = async (tx) => {
    try {
        if (tx && !tx.finished) {
            await tx.rollback();
        }
    } catch (_) {
        console.log("eerrrr");
        // swallow rollback errors to avoid masking the real error
    }
};
const addVendorService = async (req) => {
    let t = null;

    try {
        // Do non-DB work first; if this throws, t is still null
        const { filesByField, totals, fields } =
            await processMultipartAndUpload(req, {
                allowedFields: [
                    "trade_license",
                    "vat_certificate",
                    "identity_card",
                    "bank_details",
                ],
                keyPrefix: "/ourshopee-img/vendors",
                maxFilesPerField: 10,
                maxTotalFiles: 50,
                concurrency: 3,
            });

        // Only serialize if there’s actually something to store
        const kyc_files = Object.keys(filesByField || {}).length
            ? filesByField
            : null;

        // console.log("fields", fields);
        const {
            contact_person,
            contact_phone,
            email,
            category,
            category_id,
            business_address,
            emirate_id,
            area_id,
        } = fields;

        const country_id = req.country_id;

        // Start transaction right before DB work
        t = await sequelize.transaction();

        const existing = await AdminVendor.findOne({
            where: { email },
            transaction: t,
            lock: t.LOCK.UPDATE,
        });

        if (existing && existing.vendor_id > 0) {
            await safeRollback(t);
            return {
                statusCode: 200,
                message: "Vendor Already register with email!",
                status: "failure",
                data: {},
            };
        }

        const vendor = await AdminVendor.create(
            {
                contact_person,
                contact_phone,
                email,
                category_id: category_id ?? null,
                business_address: business_address ?? null,
                kyc_files, // already null or JSON string
            },
            { transaction: t }
        );

        if (vendor.vendor_id > 0) {
            const vendorLocation = await AdminVendorLocation.create(
                {
                    vendor_id: vendor.vendor_id,
                    country_id,
                    emirate_id: emirate_id ?? null,
                    area_id: area_id ?? null,
                    rstatus: 1,
                },
                { transaction: t }
            );

            if (vendorLocation.id > 0) {
                await t.commit();
                return {
                    statusCode: 200,
                    message: "Vendor onboard successfully!",
                    status: "success",
                    data: {},
                };
            }
        }

        // Failure path
        await safeRollback(t);
        return {
            statusCode: 200,
            message: "Vendor registration failed!",
            status: "failure",
            data: {},
        };
    } catch (err) {
        await safeRollback(t); // <- prevents "reading 'rollback' of undefined"
        console.error(err);
        throw err;
    }
};

const getLocationsService = async (country_id, query = {}) => {
    try {
        const where = {
            country_id,
        };

        const result = await AdminCountryEmirates.findAll({
            attributes: [
                ["emirateid", "id"],
                ["emirate", "name"],
            ],
            where,
            raw: true,
        });

        if (result.length === 0) {
            return {
                status: "success",
                statusCode: 200,
                message: "Locations fetched successfully",
                data: [{ id: 0, name: "other" }],
            };
        }

        return {
            status: "success",
            statusCode: 200,
            message: "Locations fetched successfully",
            data: result,
        };
    } catch (err) {
        throw err;
    }
};

const getAreasService = async (country_id, query = {}) => {
    try {
        // emirateid is required
        const where = {};
        if (query.emirateid) {
            // Allow both single id and CSV
            const ids = String(query.emirateid)
                .split(",")
                .map((v) => Number(v.trim()))
                .filter((n) => !isNaN(n));

            if (ids.length > 0) {
                where.emirateid = { [Op.in]: ids };
            }
        }

        const result = await AdminCountryArea.findAll({
            attributes: ["emirateid", ["area_id", "id"], "name"],
            where,
            raw: true,
        });

        if (result.length > 0) {
            return {
                status: "success",
                statusCode: 200,
                message: "Area data fetched",
                data: result,
            };
        }

        return {
            status: "success",
            statusCode: 200,
            message: "Area not found, returning default",
            data: [{ emirateid: 0, id: 0, name: "other" }],
        };
    } catch (err) {
        throw err;
    }
};

const sendEmailOtpService = async (info) => {
    try {
        const htmlContent = `
            <h3>Onboarding vendor OTP</h3>
            <p><strong>Message:</strong></p>
            <p>${info.message}</p>
        `;

        await sendEmailUsingMailGen({
            email: info.email,
            subject: `Vendor Onboarding OTP`,
            htmlContent,
        });

        return true;
    } catch (err) {
        throw err;
    }
};

export {
    addVendorService,
    getLocationsService,
    getAreasService,
    sendEmailOtpService,
};
