import crypto from "crypto";
import Decimal from "decimal.js";
import { URLSearchParams } from "url";
import axios from "axios";
import { Op } from "sequelize";

import CrmCustomer from "../models/crm_customer.model.js";
import OmsOrders from "../models/oms_orders.model.js";
import CatalogProductInventory from "../models/catalog_product_inventory.model.js";
import OmsOrderDetail from "../models/oms_order_detail.model.js";
import CatalogProduct from "../models/catalog_product.model.js";
import CatalogCategory from "../models/catalog_category.model.js";
import OmsOrderHistory from "../models/oms_order_history.model.js";

import { sendSMS, sendSMSByDoveSoft } from "./common.service.js";
import { currentDateTime } from "../utils/dateTimeHandler.js";
import { createChildOrdersBulk } from "../utils/paymentServiceHelper.js";
import {
    CURRENCY,
    countryPhonePrefixes,
    COUNTRY_NAMES,
} from "../region/config.js";

// -------------------- processOrder --------------------

export const processOrder = async (req, callUpdateStatus) => {
    try {
        console.log(req.body);
        const {
            payment_method: pmode,
            ourshopee_order_id,
            tabbyType,
            totalAmount,
            cartids,
            email,
            name,
            mobile,
            city,
            address,
            userId,
            country_id,
        } = req.body;

        const currency = CURRENCY[country_id].currency;

        // console.log(req.body)
        // Only call redirect function for specific pmodes
        if (["cash", "credit_payfort", "tabby"].includes(pmode)) {
            return handlePaymentRedirect(
                pmode,
                ourshopee_order_id,
                tabbyType,
                totalAmount,
                cartids,
                email,
                mobile,
                name,
                city,
                address,
                userId,
                callUpdateStatus,
                country_id,
                currency
            );
        } else {
            return {
                status: "skipped",
                message: "Payment method does not require redirect.",
            };
        }
    } catch (err) {
        console.error("Error in processOrder:", err);
        return { status: "error", message: err.message || "Unknown error" };
    }
};

// -------------------- handlePaymentRedirect --------------------

const handlePaymentRedirect = async (
    pmode,
    orderId,
    tabbyType,
    totalAmount,
    cartids,
    email,
    mobile,
    name,
    city,
    address,
    userId,
    callUpdateStatus,
    country_id,
    currency
) => {
    switch (pmode) {
        case "cash":
            return await confirmOrder({
                body: {
                    orderId,
                    cartids,
                    totalAmount,
                    pmode,
                    callUpdateStatus,
                    currency,
                    country_id,
                },
            });

        case "credit_payfort":
            return await handlePayfort({
                totalAmount,
                email,
                pmode,
                cartids,
                orderId,
                callUpdateStatus,
                country_id,
                currency,
            });

        case "tabby":
            return await handleTabby({
                orderId,
                cartids,
                tabbyType,
                totalAmount,
                email,
                pmode,
                mobile,
                name,
                city,
                address,
                userId,
                callUpdateStatus,
                country_id,
                currency,
            });

        default:
            return {
                status: "error",
                message: "Unsupported payment method for redirect.",
            };
    }
};

// find user registration date and loyalty level from db
const getUserDetails = async (userId) => {
    const loyaltyLevel = await OmsOrders.count({
        where: {
            customerid: userId,
            order_status: "Delivered",
        },
    });
    const customer = await CrmCustomer.findOne({
        attributes: ["date"],
        where: { id: userId },
    });
    const result = {
        registered_since: customer?.date || null,
        loyalty_level: loyaltyLevel,
    };
    // console.log(result[0][0])
    return result;
};

const handleTabby = async (req) => {
    try {
        const {
            orderId,
            cartids,
            tabbyType,
            totalAmount,
            email,
            pmode,
            mobile,
            name,
            city,
            address,
            userId,
            callUpdateStatus,
            country_id,
            currency,
        } = req;

        // console.log('[processOrder] Order Details:', {
        //     'Order ID': orderId,
        //     'Cart IDs': cartids,
        //     'Tabby Type': tabbyType,
        //     'Total Amount': totalAmount,
        //     'Email': email,
        //     'Payment Method': pmode,
        //     'Mobile': mobile,
        //     'Name': name,
        //     'City': city,
        //     'Address': address,
        //     'User ID': userId
        // });

        if (
            !cartids ||
            !totalAmount ||
            !email ||
            !pmode ||
            !mobile ||
            !name ||
            !city ||
            !address ||
            !userId ||
            !orderId
        ) {
            console.error("handleTabby Error: Missing required input data.");
            return {
                success: false,
                message: "Missing required order information.",
                status: 400,
            };
        }

        // Gather data to send to Tabby
        const purchased_at = new Date().toISOString();
        const cartList = cartids.split(",");

        const items = [];
        const productIds = [];
        const categoryIds = [];

        for (let i = 0; i < cartList.length; i++) {
            const cartItem = cartList[i].trim();

            if (cartItem !== "") {
                const parts = cartItem.split("@");

                if (parts.length === 3) {
                    productIds.push(parseInt(parts[0]));
                    items.push({
                        quantity: parseInt(parts[1]),
                        unit_price: parseFloat(parts[2]),
                    });
                }
            }
        }

        // console.log('items - ', items, 'productIds - ', productIds)

        if (productIds.length === 0) {
            console.error(
                "handleTabby Error: No valid product IDs found in cartids."
            );
            return {
                success: false,
                message: "No products found in cart.",
                status: 400,
            };
        }

        // find product names and category names from db
        const products = await CatalogProduct.findAll({
            attributes: ["title", "categoryid"],
            where: { productid: productIds },
        });

        products.forEach((product, index) => {
            items[index].title = product.title;
            categoryIds.push(product.categoryid);
        });

        const categories = await CatalogCategory.findAll({
            attributes: ["category_name"],
            where: { categoryid: categoryIds },
        });

        categories.forEach(
            (category, index) =>
                (items[index].category = category.category_name)
        );

        const { loyalty_level, registered_since } =
            await getUserDetails(userId);

        let merchant_code =
            tabbyType === 1
                ? process.env.TABBY_MERCHANT_CODE1
                : process.env.TABBY_MERCHANT_CODE2;

        if (parseInt(country_id) === 5)
            merchant_code = process.env.TABBY_MERCHANT_CODE_KWT;
        const failureParams = new URLSearchParams({
            status: "Rejected by Tabby",
            orderId,
        });
        const cancelParams = new URLSearchParams({
            status: "Cancelled by Customer",
            orderId,
        });

        const successReturnURL = `${process.env.TABBY_RESPONSE_URL}?status=success&country_id=${country_id}`;
        const cancelReturnURL =
            process.env.TABBY_RESPONSE_URL + "?" + cancelParams.toString();
        const failureReturnURL =
            process.env.TABBY_RESPONSE_URL + "?" + failureParams.toString();

        const phone = countryPhonePrefixes[country_id] + mobile;

        const buyer = { phone, email, name };
        const shipping_address = { city, address };

        //adding orderId, cartId and payment mode for confirmOrder
        const params = {
            payment: {
                amount: totalAmount,
                currency: currency,
                description: `Order ID #${orderId}, Cart ID #${cartids}, Call Update Status #${callUpdateStatus}, country_id #${country_id} `,
                buyer,
                shipping_address,
                order: {
                    reference_id: orderId,
                    items,
                },
                buyer_history: {
                    registered_since,
                    loyalty_level,
                    is_phone_number_verified: true,
                    is_email_verified: true,
                },
                order_history: [
                    {
                        purchased_at,
                        amount: totalAmount,
                        status: "new",
                        buyer,
                        shipping_address,
                    },
                ],
            },
            lang: "en",
            merchant_code,
            merchant_urls: {
                success: successReturnURL,
                cancel: cancelReturnURL,
                failure: failureReturnURL,
            },
        };

        // console.log('params', params)

        const tabbyApiUrl =
            process.env.TABBY_API_BASE_URL + process.env.TABBY_API_CHECKOUT_URL;

        let response = await axios.post(tabbyApiUrl, params, {
            headers: {
                Authorization: `Bearer ${process.env.TABBY_PUBLIC_KEY}`,
                "Content-Type": "application/json",
                Accept: "application/json",
            },
            timeout: 20000,
        });
        // console.log('tabby response', response)
        if (response.data.rejection_reason_code) {
            const reason = response.data.rejection_reason_code;
            throw {
                status: "Rejected by Tabby",
                reason,
            };
        }

        const redirectionURL =
            response.data.configuration.available_products.installments[0]
                .web_url;

        const data = {
            orderId,
            cartids,
            tabbyType,
            totalAmount,
            pmode,
            redirectionURL,
        };

        // const paymentId = response.data.payment.id
        // console.log("Tabby API Success Response Status:", response.status, paymentId)

        return data;
    } catch (error) {
        console.error("error in /api/handleTabby:", error);
        return {
            status: "error",
            message: error.reason,
        };
    }
};

// const verifyTabbyPayment = async (paymentId) => {
//     try {
//         const response = await fetch(`https://api.tabby.ai/api/v2/payments/${paymentId}`, {
//             headers: {
//                 'Authorization': `Bearer ${process.env.TABBY_SECRET_KEY}`,
//                 'Content-Type': 'application/json'
//             }
//         });

//         if (response.ok) {
//             const paymentData = await response.json();
//             return {
//                 isValid: true,
//                 status: paymentData.status,
//                 amount: paymentData.amount,
//                 currency: paymentData.currency,
//                 orderId: paymentData.order?.reference_id
//             };
//         }

//         return { isValid: false, error: 'Payment not found' };
//     } catch (error) {
//         return { isValid: false, error: error.message };
//     }
// };

export const handleTabbyResponse = async (req) => {
    if (!req.payment_id) {
        console.error("checkTabbyPaymentStatus Error: Missing tabbyPaymentId.");
        return {
            success: false,
            message: "Missing payment identifier.",
            status: 400,
        };
    }

    const baseURL = `${COUNTRY_NAMES[req.country_id]}_BASE_URL`;
    const status = req.status;

    if (req.status !== "success") {
        await OmsOrders.update(
            {
                gway_status: status,
            },
            {
                where: {
                    order_ref_code: req.orderId,
                },
            }
        );
        return {
            status,
            url:
                process.env.BASE_URL +
                process.env.REDIRECTION_FAILURE_URL +
                "?status=" +
                status,
        };
    }
    try {
        // console.log('this is the payment ID from response - ', req.payment_id)

        const paymentStatusURL = process.env.TABBY_PAYMENT_URL + req.payment_id;
        const getPaymentStatus = await axios.get(paymentStatusURL, {
            headers: {
                Authorization: `Bearer ${process.env.TABBY_SECRET_KEY}`,
                Accept: "application/json",
            },
            timeout: 10000,
        });
        const paymentStatus = getPaymentStatus.data;

        // console.log('Tabby Get Payment Status Response: ', getPaymentStatus.status, paymentStatus)

        // creating this object to send to confirmOrder
        const dataForConfirmation = { body: {} };

        dataForConfirmation.body.orderId = paymentStatus.order.reference_id;
        dataForConfirmation.body.totalAmount = paymentStatus.amount;

        // extracting info we embeded in Tabby request in handleTabby
        const dataArray = extractValuesAfterHash(paymentStatus.description);
        // console.log('dataArray', dataArray)

        dataForConfirmation.body.orderId = dataArray[0];
        dataForConfirmation.body.cartids = dataArray[1];
        dataForConfirmation.body.country_id = dataArray[3];

        // console.log(dataForConfirmation)

        if (getPaymentStatus.data.status === "CLOSED") {
            await OmsOrders.update(
                {
                    gway_transaction_id: paymentStatus.id,
                    payment_status: "Paid",
                },
                {
                    where: {
                        order_ref_code: paymentStatus.order.reference_id,
                    },
                }
            );

            const orderResult = await confirmOrder(dataForConfirmation);
            if (orderResult.status === "success") {
                return {
                    status: "success",
                    url:
                        process.env[baseURL] +
                        process.env.REDIRECTION_SUCCESS_URL +
                        paymentStatus.order.reference_id +
                        `?` +
                        `${"callUpdate=" + dataArray[2]}`,
                };
            } else {
                // throw new Error (orderResult.message)
                const failureParams = new URLSearchParams({
                    status: orderResult.message,
                });
                return {
                    status: orderResult.message,
                    url:
                        process.env[baseURL] +
                        process.env.REDIRECTION_FAILURE_URL +
                        "?" +
                        failureParams.toString(),
                };
            }
        } else {
            await OmsOrders.update(
                {
                    gway_status: getPaymentStatus.data.status,
                },
                {
                    where: {
                        order_ref_code: req.orderId,
                    },
                }
            );
            // console.log(result[0])
        }
    } catch (error) {
        console.log("error in handleTabbyResponse - ", error);
        throw error;
    }
};

function extractValuesAfterHash(input) {
    const matches = input.match(/#([^,\n]+)/g);
    if (!matches) return [];

    return matches.map((item) => item.slice(1).trim());
}

const handlePayfort = async (req) => {
    // console.log('request is here', req)
    try {
        const {
            orderId: merchant_reference,
            totalAmount,
            email: customer_email,
            pmode,
            cartids,
            callUpdateStatus,
            country_id,
            currency,
        } = req;
        // console.log(merchant_reference, totalAmount, customer_email)
        const amountInMinorUnits = calculateAmountToMinorUnits(
            totalAmount,
            currency,
            country_id
        );
        // console.log('minor usints', amountInMinorUnits)
        const merchantExtraData = `totalAmount_${totalAmount};cartids_${cartids};callUpdateStatus_${callUpdateStatus};country_id_${country_id}`;

        const params = {
            amount: amountInMinorUnits,
            currency,
            language: "en",
            command: "PURCHASE",
            customer_email,
            merchant_reference,
            access_code: process.env.PAYFORT_ACCESS_CODE,
            merchant_identifier: process.env.PAYFORT_MERCHANT_IDENTIFIER,
            return_url: process.env.PAYFORT_RESPONSE_URL,
            merchant_extra: merchantExtraData,
        };

        const signature = await calculateSignature(
            params,
            process.env.PAYFORT_SHA_REQUEST_PHRASE
        );
        params.signature = signature;
        // console.log('params', params)

        const payfortURL = process.env.PAYFORT_URL;

        return {
            payfortURL,
            params,
            pmode,
            totalAmount,
        };
    } catch (error) {
        console.error("Unhandled error in /api/handlePayfort:", error);
        return {
            success: false,
            message: "Failed to generate Payfort redirection URL",
            error: error,
        };
    }
};

const calculateAmountToMinorUnits = (amount, currencyCode, country_id) => {
    try {
        const amountDecimal = new Decimal(amount);
        const decimals = CURRENCY[country_id].minor_unit;
        // creating the deciaml multiplier 100/1000/10 etc
        const multiplier = new Decimal(10).pow(decimals);

        if (amountDecimal.decimalPlaces() > decimals) {
            throw new Error(
                `Amount '${amount}' has too many decimal places for currency ${currencyCode} (max ${decimals})`
            );
        }
        const minorUnitAmount = amountDecimal.mul(multiplier).truncated();
        // console.log(amountDecimal, decimals, multiplier, minorUnitAmount)
        return minorUnitAmount;
    } catch (error) {
        throw error;
    }
};

// cacluate hex signature for payfort
const calculateSignature = async (data, phrase) => {
    const sortedKeys = Object.keys(data).sort();
    const queryString = sortedKeys.map((key) => `${key}=${data[key]}`).join("");
    const stringToHash = `${phrase}${queryString}${phrase}`;

    const hash = crypto.createHash(process.env.PAYFORT_SHA_TYPE);
    hash.update(stringToHash);
    return hash.digest("hex");
};

export const handlePayfortResponse = async (req) => {
    // console.log(req.body, 'bodydyydydy')
    const receivedMerchantExtra = req.merchant_extra;
    const customData = { body: {} };

    if (receivedMerchantExtra) {
        receivedMerchantExtra.split(";").forEach((pair) => {
            // Split only on the first underscore assuming values don't contain it
            const splitIndex = pair.indexOf("_");
            if (splitIndex > 0) {
                let key = pair.substring(0, splitIndex);
                let value = pair.substring(splitIndex + 1);
                if (key === "country") {
                    key = "country_id";
                    value = value[3];
                }
                customData.body[key] = value;
            }
        });
        // console.log("Parsed custom data:", customData);
    }
    const baseURL = `${COUNTRY_NAMES[customData.body.country_id]}_BASE_URL`;
    try {
        if (req.response_message === "Success") {
            // console.log('[This is from Payfort]')
            // console.log(req)
            const respnoseSignature = req.signature;
            delete req.signature;

            const calculatedSignature = await calculateSignature(
                req,
                process.env.PAYFORT_SHA_RESPONSE_PHRASE
            );

            if (respnoseSignature === calculatedSignature) {
                customData.body.orderId = req.merchant_reference;

                await OmsOrders.update(
                    {
                        gway_transaction_id: req.fort_id,
                        payment_status: "Paid",
                    },
                    {
                        where: {
                            order_ref_code: req.merchant_reference,
                        },
                    }
                );

                const orderResult = await confirmOrder(customData);
                // console.log('Confirm Order Result', orderResult)
                if (orderResult.status === "success") {
                    return {
                        status: "success",
                        url:
                            process.env[baseURL] +
                            process.env.REDIRECTION_SUCCESS_URL +
                            req.merchant_reference +
                            `?` +
                            `${"callUpdate=" + customData.body["callUpdateStatus"]}`,
                    };
                } else {
                    // throw new Error (orderResult.message)

                    const failureParams = new URLSearchParams({
                        status: orderResult.message,
                    });
                    return {
                        status: orderResult.message,
                        url:
                            process.env[baseURL] +
                            process.env.REDIRECTION_FAILURE_URL +
                            "?" +
                            failureParams.toString(),
                    };
                }
            } else throw new Error("Signature does not match");
        } else {
            await OmsOrders.update(
                {
                    gway_status: req.response_message,
                },
                {
                    where: {
                        order_ref_code: req.merchant_reference,
                    },
                }
            );

            const failureParams = new URLSearchParams({
                status: req.response_message,
            });
            return {
                status: req.response_message,
                url:
                    process.env[baseURL] +
                    process.env.REDIRECTION_FAILURE_URL +
                    "?" +
                    failureParams.toString(),
            };
        }
    } catch (error) {
        throw error;
    }
};

// -------------------- confirmOrder --------------------

const confirmOrder = async (req) => {
    try {
        console.log(req, "from confirm order req");
        const orderId = req.body.orderId;
        const country_id = req.body.country_id;
        const cartids = req.body.cartids || ""; // Example: "156137@2@20@1,181879@1@19@2"
        const cartList = cartids.split(",");
        const today = currentDateTime();
        const totalAmount = req.body.totalAmount;

        // Validate orderId
        if (!orderId) {
            return { status: "error", message: "Invalid or missing orderId" };
        }

        // Get the parent order
        const parentOrder = await OmsOrders.findOne({
            where: { order_ref_code: orderId },
        });

        if (!parentOrder) {
            return { status: "error", message: "Parent order not found" };
        }

        // Parse cart items and group by vendor
        const orderProducts = [];
        const vendorGroups = {};

        for (let i = 0; i < cartList.length; i++) {
            const cartItem = cartList[i].trim();

            if (cartItem !== "") {
                const parts = cartItem.split("@");

                if (parts.length >= 3) {
                    const product_id = parseInt(parts[0]);
                    const quantity = parseInt(parts[1]);
                    const price = parseFloat(parts[2]);
                    const vendor_id = parts.length > 3 ? parseInt(parts[3]) : 0; // Default to 0 if not provided

                    const productData = {
                        product_id,
                        quantity,
                        price,
                        vendor_id,
                    };

                    orderProducts.push(productData);

                    // Group by vendor
                    if (!vendorGroups[vendor_id]) {
                        vendorGroups[vendor_id] = [];
                    }
                    vendorGroups[vendor_id].push(productData);
                }
            }
        }

        // **OPTIMIZED BULK CREATE IMPLEMENTATION**
        const createdOrders = await createChildOrdersBulk(
            vendorGroups,
            parentOrder
        );
        const allOrderDetails = [];
        console.log("createdOrders :", createdOrders);

        // Process order details for each vendor (including parent order)
        for (const [vendor_id, products] of Object.entries(vendorGroups)) {
            const vendorId = parseInt(vendor_id);
            let vendorOrder;

            // Find the corresponding order (parent or child)
            if (vendorId === 0 || Object.keys(vendorGroups).length === 1) {
                vendorOrder = parentOrder;
            } else {
                vendorOrder = createdOrders?.find(
                    (order) => order.vendor_id === vendorId
                );
            }

            if (!vendorOrder) {
                console.warn(
                    `Vendor order not found for vendor_id: ${vendorId}`
                );
                continue;
            }

            // Create order details for this vendor
            const vendorOrderDetails = [];
            let vendorSubTotal = 0;

            for (const product of products) {
                const productDetails = await CatalogProductInventory.findOne({
                    attributes: [
                        "inventory_id",
                        "cost",
                        "mrp",
                        "selling_price",
                        "vendor_id",
                    ],
                    where: {
                        productid: product.product_id,
                        country_id: country_id,
                        vendor_id: vendorId,
                    },
                });

                if (!productDetails) {
                    console.warn(
                        `Product inventory not found for product_id: ${product.product_id}, vendor_id: ${vendorId}`
                    );
                    continue;
                }

                const amount = productDetails.selling_price * product.quantity;
                vendorSubTotal += amount;

                const orderDetail = {
                    country_id: country_id,
                    orderid: vendorOrder.orderid,
                    vendor_id: vendorId,
                    inventory_id: productDetails.inventory_id,
                    productid: product.product_id,
                    cost: productDetails.cost,
                    mrp: productDetails.mrp,
                    selling_price: productDetails.selling_price,
                    quantity: product.quantity,
                    amount,
                    order_data: today,
                };

                vendorOrderDetails.push(orderDetail);
                allOrderDetails.push(orderDetail);
            }

            // Update vendor order totals
            if (vendorOrder.orderid !== parentOrder.orderid) {
                vendorOrder.sub_total = vendorSubTotal;
                vendorOrder.total_amount = vendorSubTotal; // Simplified calculation
                vendorOrder.itemcount = products.length;
                await vendorOrder.save();
            }
        }

        // Create all order details
        if (allOrderDetails.length > 0) {
            await OmsOrderDetail.bulkCreate(allOrderDetails);
        }

        // **BULK OPERATIONS FOR BETTER PERFORMANCE**

        // 1. Update parent order
        parentOrder.order_statusid = 1;
        parentOrder.order_status = "Ordered";
        parentOrder.itemcount = orderProducts.length;
        await parentOrder.save();

        // 2. Bulk update child orders status
        if (createdOrders.length > 0) {
            const childOrderIds = createdOrders.map((order) => order.orderid);
            await OmsOrders.update(
                {
                    order_statusid: 1,
                    order_status: "Ordered",
                },
                {
                    where: {
                        orderid: { [Op.in]: childOrderIds },
                    },
                }
            );
            console.log(
                `Bulk updated ${childOrderIds.length} child orders status`
            );
        }

        // 3. Bulk create order history records
        const historyRecords = [];

        // Parent order history
        historyRecords.push({
            orderid: parentOrder.orderid,
            staff_id: 0,
            staff_comments: "parent order confirmed",
            statusdate: today,
            order_status_type: "order_status",
            order_statusid: 1,
            status: "parent order confirmed",
            product_id: 0,
        });

        // Child orders history
        createdOrders.forEach((childOrder) => {
            historyRecords.push({
                orderid: childOrder.orderid,
                staff_id: 0,
                staff_comments: "child order created",
                statusdate: today,
                order_status_type: "order_status",
                order_statusid: 1,
                status: "child order created",
                product_id: 0,
            });
        });

        // Bulk create all history records
        if (historyRecords.length > 0) {
            await OmsOrderHistory.bulkCreate(historyRecords);
            console.log(
                `Bulk created ${historyRecords.length} order history records`
            );
        }

        // Send SMS notification
        const customer_details = JSON.parse(parentOrder.customer_details);
        if (parentOrder?.dataValues?.order_statusid > 0) {
            const orderCount = createdOrders.length + 1; // +1 for parent order
            const message = `Hello ${customer_details.name}! We have received your order ${orderId}${orderCount > 1 ? ` (${orderCount} orders created)` : ""}. Thank you for shopping with us.`;
            console.log("send sms", message);

            const cid = Number(country_id);
            if ([2, 3].includes(cid)) {
                sendSMSByDoveSoft(
                    country_id,
                    parentOrder.customer_contact,
                    message
                );
            } else {
                sendSMS(country_id, parentOrder.customer_contact, message);
            }
        }

        const data = {
            status: "success",
            message: "Order confirmed successfully",
            orderRefId: orderId,
            totalAmount,
            pmode: parentOrder.payment_method,
            callUpdateStatus: req.body.callUpdateStatus,
            parentOrderId: parentOrder.orderid,
            childOrders: createdOrders.map((order) => ({
                orderId: order.orderid,
                orderRefCode: order.order_ref_code,
                vendorId: order.vendor_id,
            })),
        };

        console.log("response from confirm order", data);
        return data;
    } catch (err) {
        console.error("Error in confirmOrder:", err);
        return { status: "error", message: err.message || "Unknown error" };
    }
};
