import http from "http";
import https from "https";
import axios from "axios";

import moment from "moment";
import querystring from "querystring";

import OurshopeeCountry from "../models/ourshopee_country.model.js";
import {
    COUNTRY_NAMES,
    countryPhonePrefixes,
    CURRENCY,
} from "../region/config.js";
import CatalogCategory from "../models/catalog_category.model.js";

function sendSMS(countryId, mobileNumber, message = "") {
    try {
        const countryBaseEnv = COUNTRY_NAMES[countryId];

        const username = process.env[`${countryBaseEnv}_MESSAGE_USER`];
        const password = process.env[`${countryBaseEnv}_MESSAGE_PASSWORD`];
        const sender_id = process.env[`${countryBaseEnv}_MESSAGE_SENDER`];
        const pinCode = countryPhonePrefixes[countryId];
        const mobile = `${pinCode}${Number(mobileNumber)}`;
        const baseUrl = process.env[`${countryBaseEnv}_MESSAGE_URL`];

        const get_url = `${baseUrl}?username=${username}&password=${password}&type=0&dlr=1&destination=${mobile}&source=${sender_id}&message=${encodeURIComponent(
            message
        )}`;

        http.get(get_url, function (result) {
            let apiResponse = ""; // Variable to accumulate response data

            // Collect response data in chunks
            result.on("data", function (chunk) {
                apiResponse += chunk;
            });

            // Log the complete response when all data is received
            result.on("end", function () {
                // Parse response code: expect format like "1701|+9715XXXXXXX|MSGID123"
                const responseCode = apiResponse.trim().split("|")[0];

                if (responseCode !== "1701") {
                    console.warn(
                        `Primary SMS failed with code ${responseCode}. Triggering fallback.`
                    );
                    resendSMS(countryId, mobileNumber, message);
                } else {
                    console.log("SMS sent successfully via primary API.");
                }
            });
        }).on("error", function (err) {
            console.error("Error:", err.message); // Log any errors
        });
    } catch (err) {
        throw err;
    }
}

function resendSMS(countryId, mobileNumber, message = "") {
    try {
        const countryBaseEnv = COUNTRY_NAMES[countryId];

        const username = process.env[`${countryBaseEnv}_MESSAGE_USER_1`];
        const password = process.env[`${countryBaseEnv}_MESSAGE_PASSWORD_1`];
        const sender_id = process.env[`${countryBaseEnv}_MESSAGE_SENDER_1`];
        const pinCode = countryPhonePrefixes[countryId];
        const mobile = `${pinCode}${Number(mobileNumber)}`;
        const baseUrl = process.env[`${countryBaseEnv}_MESSAGE_URL_1`];

        let get_url = `${baseUrl}?user=${username}&pwd=${password}&senderid=${sender_id}&mobileno=${mobile}&msgtext=${message}&priority=High&CountryCode=ALL`;

        http.get(get_url, function (result) {
            if (result.statusCode == 200) {
                return "success";
            } else {
            }
        }).on("error", function (err) {
            console.log(err);
            return "error";
        });
    } catch (err) {
        throw err;
    }
}

// Oman, Qatar
function sendSMSByDoveSoft(countryId, mobileNumber, message = "") {
    const pinCode = countryPhonePrefixes[countryId];
    const mobile = `${pinCode}${Number(mobileNumber)}`;

    const countryBaseEnv = COUNTRY_NAMES[countryId];

    let params;

    if (countryId == 3) {
        params = {
            username: process.env[`${countryBaseEnv}_MESSAGE_USER`],
            apikey: process.env[`${countryBaseEnv}_MESSAGE_PASSWORD`],
            to: mobile,
            sender: process.env[`${countryBaseEnv}_MESSAGE_SENDER`],
            text: message,
        };
    } else {
        params = {
            user: process.env[`${countryBaseEnv}_MESSAGE_USER`],
            key: process.env[`${countryBaseEnv}_MESSAGE_PASSWORD`],
            mobile,
            message: message,
            senderid: process.env[`${countryBaseEnv}_MESSAGE_SENDER`],
            accusage: "3",
        };
    }

    const baseUrl = process.env[`${countryBaseEnv}_MESSAGE_URL`];
    const queryString = querystring.stringify(params);
    const fullUrl = `${baseUrl}?${queryString}`;

    https
        .get(fullUrl, (res) => {
            let data = "";

            res.on("data", (chunk) => {
                data += chunk;
            });

            res.on("end", () => {
                console.log("Oman SMS Response:");
                console.log(data);
            });
        })
        .on("error", (err) => {
            console.error("Oman SMS Error:", err.message);
        });
}

async function smsWhatsAppToken() {
    try {
        const logdata = JSON.stringify({
            username: process.env.UAE_WHATSUP_USER,
            password: process.env.UAE_WHATSUP_PASSWORD,
        });

        const logconfig = {
            method: "post",
            maxBodyLength: Infinity,
            url: `${process.env.UAE_WHATSUP_URL}/auth/v1/login/`,
            headers: {
                "Content-Type": "application/json",
            },
            data: logdata,
        };

        const response = await axios.request(logconfig);
        const token = response.data.JWTAUTH;
        return token;
    } catch (err) {
        throw err;
    }
}

async function sendSMSWhatsApp(countryId, mobileNumber, message) {
    const jwtToken = await smsWhatsAppToken();

    const pinCode = countryPhonePrefixes[countryId];
    const mobile = `${pinCode}${Number(mobileNumber)}`;

    let data = JSON.stringify({
        phone: mobile,
        media: {
            type: "media_template",
            template_name: "ourshope_tran",
            lang_code: "en",
            body: [
                {
                    text: message,
                },
            ],
            button: [
                {
                    button_type: "authentication",
                    button_no: "0",
                    text: message,
                },
            ],
        },
    });

    let config = {
        method: "post",
        maxBodyLength: Infinity,
        url: `${process.env.UAE_WHATSUP_URL}/wba/v1/messages?phone`,
        headers: {
            "Content-Type": "application/json",
            Authorization: jwtToken,
        },
        data: data,
    };

    axios
        .request(config)
        .then((response) => {
            console.log(response);

            return "success";
        })
        .catch((err) => {
            return err;
        });
}

/*1: "UAE",
  2: "OMAN",
  3: "Qatar",
  4: "India",
  5: "Kuwait",
  6: "Bahrain",
  7: "Saudi",
*/

const getCountryDetails = (id) => {
    const result = OurshopeeCountry.findOne({
        attributes: [
            "id",
            "name",
            "currency",
            "website_link",
            "country_phone_code",
            "status",
        ],
        where: { id, status: 1 },
        raw: true,
    });

    return result;
};

const priceCalculator = (data) => {
    try {
        const currentZoneDate = moment().tz("Asia/Dubai");
        const currentUnix = currentZoneDate.unix();

        const promotionFrom = moment(data.from_date).isValid()
            ? moment(data.from_date).unix()
            : null;
        const promotionTo = moment(data.to_date).isValid()
            ? moment(data.to_date).unix()
            : null;

        const hasValidPromotion =
            parseFloat(data.promotion_price) > 0 &&
            promotionFrom !== null &&
            promotionTo !== null &&
            currentUnix > promotionFrom &&
            currentUnix < promotionTo;

        let displayPrice;

        if (hasValidPromotion) {
            displayPrice = parseFloat(data.promotion_price);
        } else if (
            currentUnix > (promotionFrom || 0) ||
            !promotionFrom ||
            parseFloat(data.promotion_price) === 0
        ) {
            displayPrice = parseFloat(data.special_price);
        } else {
            displayPrice = parseFloat(data.promotion_price);
        }

        const basePrice = parseFloat(data.price);
        const discount = basePrice - displayPrice;
        const percentage =
            basePrice > 0 ? Math.round((discount / basePrice) * 100) : 0;

        return {
            display_price: displayPrice,
            percentage,
        };
    } catch (err) {
        throw err;
    }
};

const addZeroes = (num) => {
    const number = Number(num);
    if (isNaN(number)) return "0.00";
    return number.toLocaleString("en", {
        useGrouping: false,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
    });
};

const buildImagePath = (image, transparentImage = "") => {
    const cdnBasePath = process.env.CDN_IMAGE_BASE_URL;
    return !transparentImage
        ? cdnBasePath + image
        : `${cdnBasePath}/ourshopee-img/ourshopee_transparant_image/${transparentImage}`;
};

const transformProductItem = (item, currency) => {
    const values = priceCalculator(item);
    // Only set countdown if to_date is valid, otherwise leave as undefined
    let countdown;
    if (item.to_date && moment(item.to_date).isValid()) {
        countdown = moment(item.to_date).format("YYYY-MM-DD HH:mm:ss");
    }

    return {
        id: item.id,
        name: item.name,
        display_price: `${values.display_price}`,
        currency,
        countdown,
        ...(item.country_id !== 3 && { old_price: item.price }),
        image: buildImagePath(item.image, item.transparent_image),
        percentage: values.percentage,
        url: `${item.url?.substring(0, 60)}/${item.sku}/`,
        sku: item.sku,
        avg_rating: item.avg_rating,
        total_ratings: item.total_ratings,
        reviews: item.reviews,
    };
};

const formatProductItemsWithoutCountDown = (product, countryId) => {
    const { id, name, price, special_price, image, url, sku } = product;
    const discount = price - special_price;
    const percentage = (discount / price) * 100;

    return {
        id,
        name,
        display_price: special_price,
        old_price: price,
        currency: CURRENCY[countryId]["currency"],
        image,
        percentage: Math.round(percentage),
        url,
        sku,
    };
};

const getCmsCustomSlidersData = (data) => {
    try {
        const imageBasePath = process.env.CDN_IMAGE_BASE_URL;
        const parsed = JSON.parse(data); // parse banner_images string

        return (
            parsed.images?.map((img) => {
                return img.webp ? imageBasePath + img.webp : "";
            }) || []
        );
    } catch (error) {
        console.error("Error parsing banner_images:", error);
        return [];
    }
};

// its for type 4
const getCmsCustomData = (data, type) => {
    try {
        const imageBasePath = process.env.CDN_IMAGE_BASE_URL;
        const parsed = JSON.parse(data); // Parse banner_images string
        const firstImage = parsed?.images?.find((img) => img?.original);
        // console.log('parsed', firstImage)

        const firstMobileImage = parsed?.mobile_images?.find(
            (img) => img?.original
        );
        const arabicImage = parsed?.arabic_images?.find((img) => img?.original); // Fixed key

        // Extract and sanitize URL
        // Extract and sanitize URL
        let completeUrl = firstImage?.inputData?.url || "";
        let url = "";

        if (/^https?:\/\//.test(completeUrl)) {
            const parsedUrl = new URL(completeUrl);
            url = parsedUrl.pathname === "/" ? completeUrl : parsedUrl.pathname;
        } else {
            url = completeUrl;
        }

        return {
            image: firstImage?.original
                ? imageBasePath + firstImage.original
                : "",
            mobile_image: firstMobileImage?.original
                ? imageBasePath + firstMobileImage.original
                : "",
            arabic_image: arabicImage?.original
                ? imageBasePath + arabicImage.original
                : "",
            url,
        };
    } catch (error) {
        console.error("Error parsing banner_images:", error);
        return {
            image: "",
            mobile_image: "",
            arabic_image: "",
            url: "",
        };
    }
};

const getBlogCustomData = (data, type) => {
    try {
        const imageBasePath = process.env.CDN_IMAGE_BASE_URL;
        const parsed = JSON.parse(data); // parse images string

        const firstImage = parsed.thumbnail_icon?.find((img) => img.webp);

        const deskTopImage = parsed.desktop_banner?.find((img) => img.webp);

        const domain = new URL(completeUrl);
        const url = domain.pathname === "/" ? completeUrl : domain.pathname;
        return {
            image: firstImage.webp ? imageBasePath + firstImage.webp : "",
            mobile_image: deskTopImage.webp
                ? imageBasePath + deskTopImage.webp
                : "",
        };
    } catch (error) {
        return {};
    }
};

const getSliderCustomData = (data) => {
    const parsed = typeof data === "string" ? JSON.parse(data) : data;
    try {
        const imageBasePath = process.env.CDN_IMAGE_BASE_URL;

        const enImages = parsed.image || [];
        const arImages = parsed.arabic_image || [];

        const result = [];

        for (const enImg of enImages) {
            const position = enImg.postion;
            const matchAr = arImages.find((ar) => ar.postion === position);

            result.push({
                position,
                image: enImg.file_url ? imageBasePath + enImg.file_url : "",
                mobile_image:
                    matchAr && matchAr.arabic_mobile_image
                        ? imageBasePath + matchAr.arabic_mobile_image
                        : "",
                url: enImg.url || matchAr?.arabic_url || "",
            });
        }

        return result;
    } catch (err) {
        return [];
    }
};

function formatOrderDate(baseDate, addDays = 0) {
    const date = moment(baseDate).add(addDays, "days");
    return (
        date.format("dddd").substring(0, 3) + // e.g., "Mon"
        ", " +
        date.format("MMMM").substring(0, 3) + // e.g., "Aug"
        " " +
        date.format("DD") // e.g., "09"
    );
}

const primaryCategoryService = async (req) => {
    const categories = await CatalogCategory.findAll({
        attributes: ["categoryid", "category_name"],
        where: { rstatus: 1, categorypid: 0 },
    });

    return {
        statusCode: 200,
        message: "Primary category data fetch successfully!",
        status: "success",
        data: categories,
    };
};

export {
    sendSMS,
    sendSMSByDoveSoft,
    resendSMS,
    sendSMSWhatsApp,
    getCountryDetails,
    priceCalculator,
    addZeroes,
    transformProductItem,
    formatProductItemsWithoutCountDown,
    getCmsCustomData,
    getBlogCustomData,
    formatOrderDate,
    getSliderCustomData,
    primaryCategoryService,
};
