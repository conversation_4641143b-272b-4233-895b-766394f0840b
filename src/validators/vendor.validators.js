export const addVendorSchema = {
    body: {
        type: "object",
        required: [
            "contact_person",
            "contact_phone",
            "email",
            "business_address",
            "emirate_id",
        ],
        properties: {
            contact_person: {
                type: "string",
                minLength: 3,
                maxLength: 255,
            },
            contact_phone: {
                type: "string",
                pattern: "^[0-9+]{7,13}$", // allow numbers and + with length check
            },
            email: {
                type: "string",
                format: "email",
            },
            category: { type: "string" },
            category_id: {
                type: "array",
                items: { type: "number" },
            },
            business_address: {
                type: "string",
                minLength: 3,
                maxLength: 255,
            },
            emirate_id: {
                type: "string",
                pattern: "^[0-9]+$", // must be numeric only
            },
            area_id: {
                type: "array",
                items: { type: "number" },
            },
        },
        additionalProperties: false,
    },
};
