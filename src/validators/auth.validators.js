const checkEmailSchema = {
    body: {
        type: "object",
        required: ["email"],
        properties: {
            email: {
                type: "string",
                format: "email",
                minLength: 8,
                maxLength: 50,
                errorMessage: {
                    type: "Email must be a string",
                    format: "Please enter valid email address",
                    minLength: "Email must be at least 8 characters long",
                    maxLength: "Email must not exceed 50 characters",
                },
            },
        },
        errorMessage: {
            required: {
                email: "Email is required",
            },
        },
    },
};

const checkMobileSchema = {
    body: {
        type: "object",
        required: ["mobile"],
        properties: {
            mobile: {
                type: "string",
                pattern: "^[0-9]+$",
                minLength: 7,
                maxLength: 13,
                errorMessage: {
                    pattern: "Only numbers are allowed",
                    minLength: "Mobile number must be at least 7 digits long",
                    minLength: "Mobile number must not exceed 13 characters",
                    type: "Mobile number must be a string",
                },
            },
        },
        errorMessage: {
            required: {
                mobile: "Mobile number is required",
            },
        },
    },
};

const verifyOTPSchema = {
    body: {
        type: "object",
        required: ["mobile", "otp"],
        properties: {
            mobile: {
                type: "string",
                pattern: "^[0-9]+$",
                minLength: 7,
                maxLength: 13,
                errorMessage: {
                    pattern: "Only numbers are allowed",
                    minLength: "Mobile number must be at least 7 digits long",
                    minLength: "Mobile number must not exceed 13 characters",
                    type: "Mobile number must be a string",
                },
            },
            otp: {
                type: "string",
                pattern: "^\\d{4,6}$",
                errorMessage: {
                    pattern: "OTP must be between 4 and 6 digits",
                    type: "OTP must be a string",
                },
            },
        },
        errorMessage: {
            required: {
                mobile: "Mobile number is required",
                otp: "OTP is required",
            },
        },
    },
};

const userSignUpSchema = {
    body: {
        type: "object",
        required: ["first_name", "email", "mobile", "password"],
        properties: {
            first_name: {
                type: "string",
                minLength: 1,
                maxLength: 25,
                errorMessage: {
                    minLength: "First name must be at least 1 digits long",
                    minLength: "First name must not exceed 25 characters",
                    type: "First name must be a string",
                },
            },
            email: {
                type: "string",
                format: "email",
                minLength: 8,
                maxLength: 50,
                errorMessage: {
                    type: "Email must be a string",
                    format: "Please enter valid email address",
                    minLength: "Email must be at least 8 characters long",
                    maxLength: "Email must not exceed 50 characters",
                },
            },
            mobile: {
                type: "string",
                pattern: "^[0-9]+$",
                minLength: 7,
                maxLength: 13,
                errorMessage: {
                    pattern: "Only numbers are allowed",
                    minLength: "Mobile number must be at least 7 digits long",
                    minLength: "Mobile number must not exceed 13 characters",
                    type: "Mobile number must be a string",
                },
            },
            password: {
                type: "string",
                minLength: 6,
                maxLength: 25,
                errorMessage: {
                    minLength: "Password must be at least 6 characters",
                    minLength: "Password must not exceed 25 characters",
                    type: "Password must be a string",
                },
            },
        },
        errorMessage: {
            required: {
                first_name: "First name is required",
                email: "Email is required",
                mobile: "Mobile number is required",
                password: "Password is required",
            },
        },
    },
};

const userSignInSchema = {
    body: {
        type: "object",
        required: ["email", "password"],
        properties: {
            email: {
                type: "string",
                format: "email",
                minLength: 8,
                maxLength: 50,
                errorMessage: {
                    type: "Email must be a string",
                    format: "Please enter valid email address",
                    minLength: "Email must be at least 8 characters long",
                    maxLength: "Email must not exceed 50 characters",
                },
            },
            password: {
                type: "string",
                // minLength: 6,
                maxLength: 25,
                errorMessage: {
                    minLength: "Password must be at least 6 characters",
                    maxLength: "Password must not exceed 25 characters",
                    type: "Password must be a string",
                },
            },
        },
        errorMessage: {
            required: {
                email: "Email is required",
                password: "Password is required",
            },
        },
    },
};

const forgotPasswordSchema = {
    body: {
        type: "object",
        required: ["email"],
        properties: {
            email: {
                type: "string",
                format: "email",
                minLength: 8,
                maxLength: 50,
                errorMessage: {
                    type: "Email must be a string",
                    format: "Please enter valid email address",
                    minLength: "Email must be at least 8 characters long",
                    maxLength: "Email must not exceed 50 characters",
                },
            },
        },
        errorMessage: {
            required: {
                email: "Email is required",
            },
        },
    },
};

const verifyForgotPasswordTokenSchema = {
    params: {
        type: "object",
        required: ["*"],
        properties: {
            token: {
                type: "string",
                minLength: 1,
                errorMessage: {
                    minLength: "Token is required",
                    type: "Token must be a string",
                },
            },
        },
        errorMessage: {
            required: {
                token: "Token is required",
            },
        },
    },
};

const resetPasswordSchema = {
    params: {
        type: "object",
        required: ["*"],
        properties: {
            token: {
                type: "string",
                minLength: 1,
                errorMessage: {
                    minLength: "Token is required",
                    type: "Token must be a string",
                },
            },
        },
        errorMessage: {
            required: {
                token: "Token is required",
            },
        },
    },
    body: {
        type: "object",
        required: ["password", "confirm_password"],
        properties: {
            password: {
                type: "string",
                minLength: 6,
                maxLength: 25,
                errorMessage: {
                    minLength: "Password must be at least 6 characters",
                    minLength: "Password must not exceed 25 characters",
                    type: "Password must be a string",
                },
            },
            confirm_password: {
                type: "string",
                minLength: 6,
                maxLength: 25,
                errorMessage: {
                    minLength: "Confirm password must be at least 6 characters",
                    minLength: "Confirm password must not exceed 25 characters",
                    type: "ConfirmpPassword must be a string",
                },
            },
        },
        errorMessage: {
            required: {
                password: "Password is required",
                confirm_password: "Confirm password is required",
            },
        },
    },
};

export {
    checkEmailSchema,
    checkMobileSchema,
    verifyOTPSchema,
    userSignUpSchema,
    userSignInSchema,
    forgotPasswordSchema,
    verifyForgotPasswordTokenSchema,
    resetPasswordSchema,
};
