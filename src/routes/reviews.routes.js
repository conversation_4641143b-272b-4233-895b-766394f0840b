import {
    addProductReviewController,
    deleteProductReviewController,
    editProductReviewController,
    getProductReviewByIdController,
    getUserReviewsController,
    updateDownVoteByIdController,
    updateVoteByIdController,
} from "../controllers/reviews.controllers.js";

import { verifyJWT } from "../middlewares/auth.middlewares.js";
import { uploadFile } from "../middlewares/s3.middlewares.js";

export default async function reviewRoutes(fastify) {
    // Add Review
    fastify.post(
        "/add-review",
        {
            onRequest: [verifyJWT],
            preHandler: uploadFile("images", 5),
        },
        addProductReviewController
    );

    // Edit Review (uncomment if you need this)
    fastify.put(
        "/edit-review",
        {
            onRequest: [verifyJWT],
            preHandler: uploadFile("images", 5),
        },
        editProductReviewController
    );

    // Delete Review (set rstatus = 3 internally)
    fastify.delete(
        "/delete-review",
        {
            onRequest: [verifyJWT],
        },
        deleteProductReviewController
    );

    // Get Reviews - PUBLIC
    fastify.get("/get-review/:productid", getProductReviewByIdController);

    // Add Vote to Review
    fastify.put(
        "/review/vote-like",
        {
            onRequest: [verifyJWT],
        },
        updateVoteByIdController
    );
    fastify.put(
        "/review/vote-dislike",
        {
            onRequest: [verifyJWT],
        },
        updateDownVoteByIdController
    );
    fastify.get(
        "/user-reviews",
        {
            onRequest: [verifyJWT],
        },
        getUserReviewsController
    );
    fastify.put(
        "/review/vote",
        {
            onRequest: [verifyJWT],
        },
        updateVoteByIdController
    );
}
