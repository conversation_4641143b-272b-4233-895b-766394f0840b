import {
    addVendor,
    getAreas,
    getLocations,
    primaryCategory,
    sendOtpAndEmail,
} from "../controllers/vendor.controllers.js";
import { addVendorSchema } from "../validators/vendor.validators.js";

import multipart from "@fastify/multipart";

export default async function vendorRoutes(fastify) {
    // Register once at app or module level
    // fastify.register(multipart, {
    //     limits: { fileSize: 10 * 1024 * 1024, files: 50 }, // 10MB per file, 50 files total
    // });

    fastify.post("/add-vendor", addVendor);
    fastify.post("/send-otps", sendOtpAndEmail);
    fastify.get("/categories", primaryCategory);

    fastify.get("/get-emirates", getLocations);
    fastify.get("/get-areas", getAreas);
}
