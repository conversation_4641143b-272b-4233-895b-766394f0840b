/* const common_config_data = [
    {
        type: "home_category_section",
        categories: [
            {
                category_id: 38,
                subcategory: [212, 213, 214, 215, 216, 217],
            },
            {
                category_id: 36,
                subcategory: [176, 177, 178, 179, 180, 181],
            },
            {
                category_id: 39,
                subcategory: [219, 220, 221, 222, 223, 437],
            },
            {
                category_id: 10,
                subcategory: [33, 34, 281, 284, 285, 286],
            },
            {
                category_id: 46,
                subcategory: [400, 401, 402, 403, 404, 405],
            },
            {
                category_id: 49,
                subcategory: [63, 130, 329, 331, 332, 334],
            },
            {
                category_id: 27,
                subcategory: [112, 114, 431, 442, 443, 444],
            },
            {
                category_id: 41,
                subcategory: [233, 234, 236, 237, 238, 239],
            },
            {
                category_id: 32,
                subcategory: [138, 139, 140, 141, 142, 143],
            },
        ],
    },
    {
        type: "pre-owned-banners",
        items: {
            desktopImage:
                "https://www.ourshopee.com/images/pre-owned/Pre-owned-banner.jpg",
            mobileImage:
                "https://www.ourshopee.com/images/pre-owned/Pre-owned-banner-mob.jpg",
            carouselItems: [
                {
                    id: 1,
                    url: "/products-category/Pre-Owned-Mobiles/",
                    banner_image:
                        "https://www.ourshopee.com/images/pre-owned/Pre-Owned-Mobiles.jpg",
                },
                {
                    id: 2,
                    url: "/products-category/Pre-Owned-Laptops/",
                    banner_image:
                        "https://www.ourshopee.com/images/pre-owned/Pre-Owned-Laptops.jpg",
                },
                {
                    id: 3,
                    url: "/products-category/Pre-Owned-Printers/",
                    banner_image:
                        "https://www.ourshopee.com/images/pre-owned/Pre-Owned-Printers.jpg",
                },
                {
                    id: 4,
                    url: "/products-category/Pre-Owned-Tablets/",
                    banner_image:
                        "https://www.ourshopee.com/images/pre-owned/Pre-Owned-Tablets.jpg",
                },
            ],
        },
    },
]; */

const configRegionData = {
    1: [
        {
            type: "deal_of_the_day",
            section_id: 29,
            limit: 3,
        },
        {
            type: "exciting_offers",
            section_id: "",
            limit: 24,
        },
        {
            type: "bundle_deals",
            section_id: 19,
            limit: 12,
        },
        {
            type: "clearance_sale",
            section_id: 117,
            limit: 4,
        },
        {
            type: "category_items",
            limit: 10,
        },
        {
            type: "getcart_items",
            section_id: 51,
        },
    ],
    2: [
        {
            type: "deal_of_the_day",
            section_id: 43,

            limit: 3,
        },
        {
            type: "exciting_offers",
            section_id: "",
            limit: 24,
        },
        {
            type: "bundle_deals",
            section_id: 120,
            limit: 3,
        },
        {
            type: "clearance_sale",
            section_id: 118,
            limit: 4,
        },
        {
            type: "category_items",
            limit: 10,
        },
        {
            type: "getcart_items",
            section_id: 51,
        },
    ],
    3: [
        {
            type: "deal_of_the_day",
            section_id: 33,

            limit: 3,
        },
        {
            type: "exciting_offers",
            section_id: "",
            limit: 24,
        },
        {
            type: "bundle_deals",
            section_id: 35,
            limit: 3,
        },
        {
            type: "clearance_sale",
            section_id: 126,
            limit: 4,
        },
        {
            type: "category_items",
            limit: 10,
        },
        {
            type: "getcart_items",
            section_id: 51,
        },
    ],
    5: [
        {
            type: "deal_of_the_day",
            section_id: 72,
            limit: 3,
        },
        {
            type: "exciting_offers",
            section_id: "",
            limit: 24,
        },
        {
            type: "bundle_deals",
            section_id: 171,
            limit: 3,
        },
        {
            type: "clearance_sale",
            section_id: 174,
            limit: 4,
        },
        {
            type: "category_items",
            limit: 10,
        },
        {
            type: "getcart_items",
            section_id: 51,
        },
    ],
    6: [
        {
            type: "deal_of_the_day",
            section_id: 67,
            limit: 3,
        },
        {
            type: "exciting_offers",
            section_id: "",
            limit: 24,
        },
        {
            type: "bundle_deals",
            section_id: 64,
            limit: 3,
        },
        {
            type: "clearance_sale",
            section_id: 89,
            limit: 4,
        },
        {
            type: "category_items",
            limit: 10,
        },
        {
            type: "getcart_items",
            section_id: 51,
        },
    ],
};

const countryPhonePrefixes = {
    1: "+971",
    2: "+968",
    3: "+974",
    5: "+965",
    6: "+973",
};

/* const TABLE_PRE_FIX_CMS = {
    1: "",
    2: "oman_cms",
    3: "qatar_cms",
    5: "kuwait_cms",
    6: "bahrain_cms",
}; 

const TABLE_PRE_FIX_SM = {
    1: "",
    2: "oman_sm",
    3: "qatar_sm",
    5: "kuwait_sm",
    6: "bahrain_sm",
}; */

const CURRENCY = {
    1: { currency: "AED", minor_unit: 2 },
    2: { currency: "OMR", minor_unit: 3 },
    3: { currency: "QAR", minor_unit: 2 },
    5: { currency: "KWD", minor_unit: 3 },
    6: { currency: "BHD", minor_unit: 3 },
};

const ORDER_FROM_ID = {
    Web: 1,
    Android: 2,
    iOS: 3,
    WhatsApp: 4,
    Facebook: 5,
    WebFeed: 6,
    Telephone: 7,
    Fbfeedlist: 8,
    "Webfeed-OS": 9,
    Other: 11,
    Replacemant: 12,
};

const VAT = {
    1: 5,
    2: 5,
    3: 0,
    5: 0,
    // 6: 10,       this is the actual vat but removed it for now to match the production calulations
    6: 5,
};

const FREE_SHIPPING_SUBTOTAL_VALUE = {
    mobileFest: {
        1: 150,
        2: 10,
        3: 100,
        5: 10,
        6: 20,
    },
    eidClearanceSale: {
        1: 150,
        2: 10,
        3: 100,
        5: 10,
        6: 10,
    },
};

const SHOW_COD_FINAL_TOTAL_VALUE = {
    1: 1000,
    2: 100,
    3: 1000,
    5: 150,
    6: 100,
};

const PROCESSING_FEE_COD = {
    1: [{ max: 500, value: 2 }],
    2: [{ max: 50, value: 0.5 }],
    3: [
        { max: 100, value: 2 },
        { max: 150, value: 3 },
        { max: 200, value: 4 },
        { max: 250, value: 5 },
        { max: 300, value: 6 },
        { max: 350, value: 7 },
        { max: 400, value: 8 },
        { max: 450, value: 9 },
    ],
    5: [
        { max: 10, value: "0.20" },
        { max: 20, value: "0.40" },
        { max: 30, value: "0.60" },
        { max: 40, value: "0.80" },
    ],
    6: [{ max: 50, value: 0.5 }],
};

// 183 is televisions and rest are mobile subCats
const FREE_SHIPPING_SUBCATEGORIES = [2, 5, 24, 38, 41, 183];

const sectionConfig = {
    1: {
        clearance_sale_sec_id: 117,
        deal_of_the_day_sec_id: 28,
        get_special_deal_sec_id: 29,
        get_special_deal_check_sec_id: 29,
        get_special_deal_check_in_sec_id: [165, 16, 19, 82],
        top_selling_products_sec_id: 74,
        saver_zone1_sku_list: [
            "PJ6770",
            "PJ6771",
            "PJ6772",
            "PJ6773",
            "PJ6774",
            "PJ6775",
            "PJ6776",
            "PJ6777",
            "PJ6778",
            "PJ6779",
            "PJ6780",
            "PJ6781",
            "PJ6782",
            "PJ6783",
            "PJ6784",
            "PJ6785",
            "PJ6786",
            "PJ6787",
            "PJ6788",
            "PJ6789",
            "PJ6790",
            "PJ6791",
            "PJ6792",
            "PJ6793",
            "PJ6794",
            "PJ6795",
            "PJ6796",
            "PJ6797",
            "PJ6798",
            "PJ6799",
            "PJ6800",
            "PJ6801",
            "PJ6802",
            "PJ6803",
            "PJ6804",
            "PJ6805",
            "PJ6806",
            "PJ6807",
            "PJ6808",
            "PJ6809",
            "PJ6810",
            "PJ6811",
            "PJ6812",
            "PJ6813",
        ],
        get_infinte_scroll_tems: { type: 205, subcategory_id: 24 },
    },
    2: {
        clearance_sale_sec_id: 118,
        deal_of_the_day_sec_id: 42,
        get_special_deal_sec_id: 43,
        get_special_deal_check_sec_id: 43,
        get_special_deal_check_in_sec_id: [185, 55, 120, 83],
        top_selling_products_sec_id: 75,
        saver_zone1_sku_list: [
            "OY2727",
            "OY2728",
            "OY2729",
            "OY2730",
            "OY2731",
            "OY2732",
            "OY2733",
            "OY2734",
            "OY2735",
            "OY2736",
            "OY2737",
            "OY2738",
            "OY2739",
            "OY2740",
            "OY2741",
            "OY2742",
            "OY2743",
            "OY2744",
            "OY2745",
            "OY2746",
            "OY2747",
            "OY2748",
            "OY2749",
            "OY2750",
            "OY2751",
            "OY2752",
            "OY2753",
            "OY2754",
            "OY2755",
            "OY2756",
            "OY2757",
            "OY2758",
            "OY2759",
            "OY2760",
            "OY2761",
            "OY2762",
            "OY2763",
            "OY2764",
            "OY2765",
            "OY2766",
            "OY2767",
            "OY2768",
            "OY2769",
            "OY2770",
            "OY2771",
            "OY2772",
            "OY2773",
            "OY2774",
            "OY2775",
            "OY2776",
            "OY2777",
            "OY2778",
            "OY2779",
            "OY2780",
            "OY2781",
            "OY2782",
            "OY2783",
            "OY2784",
        ],
        get_infinte_scroll_tems: { type: 157, subcategory_id: 34 },
    },
    3: {
        clearance_sale_sec_id: 126,
        deal_of_the_day_sec_id: 32,
        get_special_deal_sec_id: 56,
        get_special_deal_check_sec_id: 56,

        get_special_deal_check_in_sec_id: [145, 60, 35, 97],
        top_selling_products_sec_id: 76,
        saver_zone1_sku_list: [
            "PJ7459",
            "PJ7460",
            "PJ7461",
            "PJ7462",
            "PJ7463",
            "PJ7464",
            "PJ7465",
            "PJ7466",
            "PJ7467",
            "PJ7468",
            "PJ7469",
            "PJ7470",
            "PJ7471",
            "PJ7472",
            "PJ7473",
            "PJ7474",
            "PJ7475",
            "PJ7476",
            "PJ7477",
            "PJ7478",
            "PJ7479",
            "PJ7480",
            "PJ7481",
            "PJ7482",
            "PJ7483",
            "PJ7484",
            "PJ7485",
            "PJ7486",
            "PJ7487",
            "PJ7488",
            "PJ7489",
            "PJ7490",
            "PJ7491",
            "PJ7492",
            "PJ7493",
            "PJ7494",
            "PJ7495",
            "PJ7496",
            "PJ7497",
            "PJ7498",
            "PJ7499",
            "PJ7500",
            "PJ7501",
            "PJ7502",
            "PJ7503",
            "PJ7504",
            "PJ7505",
            "PJ7506",
            "PJ7507",
            "PJ7508",
        ],
        get_infinte_scroll_tems: { type: 154, subcategory_id: 34 },
    },
    5: {
        clearance_sale_sec_id: 174,
        deal_of_the_day_sec_id: 70,
        get_special_deal_sec_id: 72,
        get_special_deal_check_sec_id: 72,

        get_special_deal_check_in_sec_id: [187, 84, 171, 12],
        top_selling_products_sec_id: 78,
        saver_zone1_sku_list: [
            "PF9917",
            "PF9918",
            "PF9919",
            "PF9920",
            "PF9921",
            "PF9922",
            "PF9923",
            "PF9924",
            "PF9925",
            "PF9926",
            "PF9927",
            "PF9928",
            "PF9929",
            "PF9930",
            "PF9931",
            "PF9932",
            "PF9933",
            "PF9934",
            "PF9935",
            "PF9936",
            "PF9937",
            "PF9938",
            "PF9939",
            "PF9940",
            "PF9941",
            "PF9942",
            "PF9943",
            "PF9944",
            "PF9945",
            "PF9946",
            "PF9947",
            "PF9948",
            "PF9949",
            "PF9950",
            "PF9951",
            "PF9952",
            "PF9953",
            "PF9954",
            "PF9955",
            "PF9956",
            "PF9957",
            "PF9958",
            "PF9959",
            "PF9960",
            "PF9961",
            "PF9962",
            "PF9963",
            "PF9964",
        ],
        get_infinte_scroll_tems: { type: 160, subcategory_id: 34 },
    },
    6: {
        clearance_sale_sec_id: 89,
        deal_of_the_day_sec_id: 66,
        get_special_deal_sec_id: 67,
        get_special_deal_check_sec_id: 67,
        get_special_deal_check_in_sec_id: [186, 73, 64, 115],
        top_selling_products_sec_id: 77,
        saver_zone1_sku_list: [
            "PJ7278",
            "PJ7279",
            "PJ7280",
            "PJ7281",
            "PJ7282",
            "PJ7283",
            "PJ7284",
            "PJ7285",
            "PJ7286",
            "PJ7287",
            "PJ7288",
            "PJ7289",
            "PJ7290",
            "PJ7291",
            "PJ7292",
            "PJ7293",
            "PJ7294",
            "PJ7295",
            "PJ7296",
            "PJ7297",
            "PJ7298",
            "PJ7299",
            "PJ7300",
            "PJ7301",
            "PJ7302",
            "PJ7303",
            "PJ7304",
            "PJ7305",
            "PJ7306",
            "PJ7307",
            "PJ7308",
            "PJ7309",
            "PJ7310",
            "PJ7311",
            "PJ7312",
            "PJ7313",
            "PJ7314",
            "PJ7315",
            "PJ7316",
            "PJ7317",
            "PJ7318",
            "PJ7319",
            "PJ7320",
            "PJ7321",
            "PJ7322",
            "PJ7323",
            "PJ7324",
            "PJ7325",
        ],
        get_infinte_scroll_tems: { type: 206, subcategory_id: 34 },
    },
};
const DYNAMIC_BANNERS_TYPE_ID = {
    1: 22,
    2: 23,
    3: 24,
    5: 25,
    6: 26,
};

const DYNAMIC_BANNERS_KEYS = {
    1: "heroBanner",
    2: "sectionBanner1",
    3: "sectionBanner2",
    4: "sectionBanner3",
    5: "tabBanner1",
    6: "tabBanner2",
    7: "tabBanner3",
    8: "brandWeekBg",
    9: "brandWeekImg",
    10: "mainBanner1",
    11: "mainBanner2",
    12: "mainBanner3",
    13: "mainBanner4",
    14: "mainBanner5",
};

const getHomeTopSelling = {
    1: { section_id: 74, offset: 0, limit: 15 },
    2: { section_id: 75, offset: 0, limit: 4 },
    3: { section_id: 75, offset: 0, limit: 4 },
    4: { section_id: 78, offset: 0, limit: 4 },
    5: { section_id: 78, offset: 0, limit: 4 },
    6: { section_id: 77, offset: 0, limit: 4 }, // Adding Bahrain configuration
};

const COUNTRY_NAMES = {
    1: "UAE",
    2: "OMAN",
    3: "QATAR",
    5: "KUWAIT",
    6: "BAHRAIN",
};

const RATING_VOTES = {
    LIKE: "like",
    DISLIKE: "dislike",
};

const REVIEW_STATUS = {
    PENDING: 0,
    ACCEPTED: 1,
    REJECTED: 2,
    SOFT_DELETED: 3,
};

export {
    // common_config_data,
    // TABLE_PRE_FIX_CMS,
    // TABLE_PRE_FIX_SM,
    configRegionData,
    countryPhonePrefixes,
    CURRENCY,
    sectionConfig,
    getHomeTopSelling,
    VAT,
    FREE_SHIPPING_SUBTOTAL_VALUE,
    PROCESSING_FEE_COD,
    SHOW_COD_FINAL_TOTAL_VALUE,
    FREE_SHIPPING_SUBCATEGORIES,
    ORDER_FROM_ID,
    COUNTRY_NAMES,
    DYNAMIC_BANNERS_TYPE_ID,
    DYNAMIC_BANNERS_KEYS,
    RATING_VOTES,
    REVIEW_STATUS,
};
